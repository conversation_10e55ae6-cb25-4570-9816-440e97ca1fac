import { DataTable, setDefaultTimeout } from '@cucumber/cucumber';
import { HttpRequestConfig } from '@superawesome/freekws-common-http-client';
import { SALogger } from '@superawesome/freekws-common-logger';
import { FamiliesUserAddedToFamilyDTO } from '@superawesome/freekws-queue-messages/webhook';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';
import assert from 'assert';
import { binding, given, then, when } from 'cucumber-tsflow';

import { OrgLevelWebhookPayload } from '../../src/webhook/types';
import { Utils } from '../../test/utils';
import { orgEnvHost } from '../fixtures/org-env.fixture';
import { UATUtils } from '../utils';

setDefaultTimeout(60 * 1000);
@binding([SALogger])
export class UserAddedToFamilySteps {
  userId: string;
  responseStatus: number;
  orgId: string;
  secret: string;

  @given('the organization exists with expected secret')
  the_org_exists_with_expected_secret(table: DataTable) {
    const data = table.rowsHash() as {
      orgId: string;
      secret: string;
    };

    this.orgId = data.orgId;
    this.secret = data.secret;
  }

  @when('user-added-to-family for {string} is triggered')
  async user_added_to_family_triggered(userId: string): Promise<void> {
    const timestamp = Date.now();
    const body: OrgLevelWebhookPayload<FamiliesUserAddedToFamilyDTO> = {
      name: EWebhookName.FAMILIES_USER_ADDED_TO_FAMILY,
      time: timestamp,
      orgId: this.orgId,
      payload: {
        userId: userId,
      },
    };
    const signature = Utils.generateKwsSignature(timestamp, body, this.secret);

    const request = {
      url: `/v1/webhooks/user-added-to-family`,
      method: 'POST',
      data: body,
      headers: {
        'x-kws-signature': `t=${timestamp},v1=${signature}`,
        'x-forwarded-host': orgEnvHost,
      },
    } as HttpRequestConfig;

    const response = await UATUtils.buildClassicWrapperClient().request(request);
    this.responseStatus = response.status;
  }

  @then('user-added-to-family returns http status code of no-content')
  user_added_to_family_returns_no_content() {
    assert.equal(this.responseStatus, 204);
  }
}
