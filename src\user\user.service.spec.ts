import { TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AgeGateDTO } from '@superawesome/freekws-agegate-api-common';
import { PendingGuardianLinkDTO } from '@superawesome/freekws-family-service-common/types/family-group/internal-admin-family-group.dto';
import { EUserContext } from '@superawesome/freekws-nestjs-guards';
import { VerificationDTO } from '@superawesome/freekws-preverification-service-common';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';
import * as crypto from 'crypto';
import { IsNull, Repository, UpdateResult } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import SpyInstance = jest.SpyInstance;
import { Activation } from './activation.entity';
import { UserUpdateParams } from './types';
import { UserAppActivationDTO, UserRegisterDTO } from './user.dto';
import { User } from './user.entity';
import { UserService } from './user.service';
import { App } from '../app/app.entity';
import { AppService } from '../app/app.service';
import { IAppInfo } from '../app/types';
import { AgeGateService } from '../common/services/age-gate/age-gate.service';
import { AnalyticService } from '../common/services/analytic/analytic.service';
import { FamilyGroupService } from '../common/services/family-group/family-group.service';
import { PreVerificationService } from '../common/services/pre-verification/pre-verification.service';
import { SettingsService } from '../common/services/settings/settings.service';
import { Testing } from '../common/utils';
import { OrgEnv } from '../org-env/org-env.entity';
import { IClientCredentials } from '../org-env/types';
import { WebhookService } from '../webhook/webhook.service';

describe('UserService', () => {
  let service: UserService;
  let userRepo: Repository<User>;
  let appRepo: Repository<App>;
  let activationRepo: Repository<Activation>;
  let familyService: FamilyGroupService;
  let verificationService: PreVerificationService;
  let settingsService: SettingsService;
  let webhookService: WebhookService;
  let appService: AppService;
  let analyticsService: AnalyticService;
  let ageGateService: AgeGateService;
  const orgEnvId = uuidv4();

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [UserService],
    });

    service = module.get<UserService>(UserService);
    userRepo = module.get(getRepositoryToken(User));
    appRepo = module.get(getRepositoryToken(App));
    activationRepo = module.get(getRepositoryToken(Activation));
    familyService = module.get(FamilyGroupService);
    verificationService = module.get(PreVerificationService);
    settingsService = module.get(SettingsService);
    webhookService = module.get(WebhookService);
    appService = module.get(AppService);
    analyticsService = module.get(AnalyticService);
    ageGateService = module.get(AgeGateService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should return created user', async () => {
      jest.spyOn(userRepo, 'save').mockImplementationOnce(async (user: User) => {
        Object.assign(user, {
          id: 1,
          uuid: '00000000-0000-0000-0000-000000000000',
          parentVerified: false,
          parentExpired: false,
          parentRejected: false,
          parentIdVerified: false,
          parentDeleted: false,
          activations: [],
        });
        return user;
      });

      await expect(
        service.create({
          orgEnvId: '1',
          appId: 1,
          dateOfBirth: '2010-10-10',
          language: 'en',
          signUpCountry: 'GB',
        }),
      ).resolves.toEqual({
        activations: [],
        createdAt: undefined,
        dateOfBirth: expect.any(Date),
        externalId: undefined,
        id: 1,
        language: 'en',
        orgEnv: {
          id: '1',
        },
        orgEnvId: '1',
        parentDeleted: false,
        parentEmail: undefined,
        parentExpired: false,
        parentIdVerified: false,
        parentRejected: false,
        parentVerified: false,
        password: undefined,
        passwordResetToken: undefined,
        passwordResetTokenExpiry: undefined,
        signUpCountry: 'GB',
        updatedAt: undefined,
        username: undefined,
        uuid: '00000000-0000-0000-0000-000000000000',
      });
    });
  });

  describe('getById', () => {
    it('should return user entity', async () => {
      jest.spyOn(userRepo, 'findOneBy').mockResolvedValueOnce(new User());

      await expect(service.getById(orgEnvId, 1)).resolves.toBeInstanceOf(User);
    });
  });

  describe('updateWithMissingValues', () => {
    it('should update with changes', async () => {
      const newDate = new Date('2010-10-10');
      const spy = jest.spyOn(userRepo, 'update');
      await service.updateWithMissingValues(orgEnvId, 1, { dateOfBirth: newDate });

      expect(spy).toHaveBeenCalledWith({ id: 1, orgEnvId, dateOfBirth: IsNull() }, { dateOfBirth: newDate });
    });

    it('should not update without changes', async () => {
      const spy = jest.spyOn(userRepo, 'update');
      await service.updateWithMissingValues(orgEnvId, 1);

      expect(spy).not.toHaveBeenCalled();
    });

    it('should not update with random fields', async () => {
      const spy = jest.spyOn(userRepo, 'update');
      await service.updateWithMissingValues(orgEnvId, 1, {
        uuid: '0000000-0000-0000-000000000000',
        dateOfBirth: undefined,
      } as unknown as UserUpdateParams);

      expect(spy).not.toHaveBeenCalled();
    });
  });

  describe('getParentEmail', () => {
    it('should return parent email if user family contains a manager', async () => {
      jest.spyOn(familyService, 'getGuardianLinks').mockResolvedValueOnce({
        pendingRequests: [
          {
            email: '<EMAIL>',
          },
        ] as unknown as PendingGuardianLinkDTO[],
        confirmedRequests: [],
      });

      await expect(service.getParentEmail(1)).resolves.toEqual('<EMAIL>');
    });

    it('should return nothing if user in family without manager', async () => {
      jest.spyOn(familyService, 'getGuardianLinks').mockResolvedValueOnce({
        pendingRequests: [],
        confirmedRequests: [],
      });

      await expect(service.getParentEmail(1)).resolves.toBeUndefined();
    });
  });

  describe('getParentState', () => {
    it('should return verified parent state', async () => {
      jest
        .spyOn(verificationService, 'getVerificationsByEmail')
        .mockResolvedValueOnce([{ userContext: EUserContext.Parent } as VerificationDTO]);

      await expect(service.getParentState('<EMAIL>', 'org-id')).resolves.toEqual({
        verified: false,
        idVerified: true,
        deleted: false,
        rejected: false,
        expired: false,
      });
    });

    it('should return not verified parent state', async () => {
      jest.spyOn(verificationService, 'getVerificationsByEmail').mockResolvedValueOnce([]);

      await expect(service.getParentState('<EMAIL>', 'org-id')).resolves.toEqual({
        verified: false,
        idVerified: false,
        deleted: false,
        rejected: false,
        expired: false,
      });
    });
  });

  describe('deleteActivation', () => {
    const user = { id: 1, orgEnv: { id: '123', orgId: '1' } as OrgEnv } as User;
    const credentials = { clientId: 'client-id', secret: 'top-secret' } as IClientCredentials;
    const app = {
      id: 2,
      productId: 123,
      orgEnv: {
        clientId: credentials.clientId,
        clientSecret: credentials.secret,
      },
    } as unknown as App;
    const settings = [
      {
        namespace: 'someNamespace',
        settingName: 'permission',
      },
    ] as UserSettingValueDTO[];
    const activation = { app, user } as Activation;

    beforeEach(() => {
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce(settings);

      jest.spyOn(userRepo, 'findOneOrFail').mockResolvedValueOnce(user);
      jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValueOnce(app);
      jest.spyOn(activationRepo, 'findOneOrFail').mockResolvedValueOnce(activation);
    });

    it('Deletes activation', async () => {
      const activationDeleteSpy = jest.spyOn(activationRepo, 'delete');
      const userRepoDelete = jest.spyOn(userRepo, 'delete');
      jest.spyOn(activationRepo, 'count').mockResolvedValueOnce(2);

      await service.deleteActivation(orgEnvId, 1, 2);

      expect(activationDeleteSpy).toHaveBeenCalledWith(activation);
      expect(userRepoDelete).not.toHaveBeenCalled();
    });

    it('Triggers child-activation-deleted webhook after deleting activation', async () => {
      const sendChildActivationDeleted = jest.spyOn(webhookService, 'sendChildActivationDeleted');
      jest.spyOn(activationRepo, 'count').mockResolvedValueOnce(2);

      await service.deleteActivation(orgEnvId, 1, 2);

      expect(sendChildActivationDeleted).toHaveBeenCalledTimes(1);
    });

    it('Deletes user if deleting final activation', async () => {
      const userRepoDelete = jest.spyOn(userRepo, 'delete');
      const settingsDeleteUser = jest.spyOn(settingsService, 'deleteUser');
      jest.spyOn(activationRepo, 'count').mockResolvedValueOnce(1);

      await service.deleteActivation(orgEnvId, 1, 2);

      expect(userRepoDelete).toHaveBeenCalled();
      expect(settingsDeleteUser).toHaveBeenCalled();
    });

    it('Triggers user-account-deleted webhook after deleting user', async () => {
      const sendUserDeleted = jest.spyOn(webhookService, 'sendUserAccountDeleted');
      jest.spyOn(activationRepo, 'count').mockResolvedValueOnce(1);

      await service.deleteActivation(orgEnvId, 1, 2);

      expect(sendUserDeleted).toHaveBeenCalledTimes(1);
    });
  });

  describe('updateDateOfBirth', () => {
    it('should return true when changes were made', async () => {
      jest.spyOn(userRepo, 'update').mockResolvedValueOnce({ affected: 1 } as UpdateResult);

      await expect(service.updateDateOfBirth(orgEnvId, 3, new Date())).resolves.toBeTruthy();
    });

    it('should return false when changes were not made', async () => {
      jest.spyOn(userRepo, 'update').mockResolvedValueOnce({ affected: 0 } as UpdateResult);

      await expect(service.updateDateOfBirth(orgEnvId, 3, new Date())).resolves.toBeFalsy();
    });
  });

  describe('activateUserToApp', () => {
    const mockUser = {
      id: 1,
      signUpCountry: 'GB',
      language: 'en',
    } as User;

    const mockApp = {
      id: 2,
      name: 'test-app',
      orgEnvId: '123',
    } as App;

    const mockCredentials = {
      clientId: 'test-client-id',
      secret: 'test-secret',
    };

    const mockParentEmail = '<EMAIL>';

    beforeEach(() => {
      jest.spyOn(userRepo, 'findOneOrFail').mockResolvedValueOnce(mockUser);

      jest.spyOn(appService, 'getAppInfoBy').mockResolvedValueOnce({
        app: mockApp,
        credentials: mockCredentials,
      } as IAppInfo);

      jest.spyOn(activationRepo, 'save').mockResolvedValueOnce({
        user: mockUser,
        app: mockApp,
      } as Activation);
    });

    it('activates user to app with permissions', async () => {
      jest.spyOn(familyService, 'getGuardianLinks').mockResolvedValueOnce({
        pendingRequests: [
          {
            email: mockParentEmail,
          },
        ] as unknown as PendingGuardianLinkDTO[],
        confirmedRequests: [],
      });
      const requestPermissionsForUserSpy = jest.spyOn(appService, 'requestPermissionsForUser');
      requestPermissionsForUserSpy.mockResolvedValueOnce({ userSettings: [] } as unknown as ReturnType<
        typeof appService.requestPermissionsForUser
      >);
      const activationSuccessSpy = jest.spyOn(analyticsService, 'activationSuccess');

      const activationRequestWithPermissions: UserAppActivationDTO = {
        appName: 'test-app',
        permissions: ['read', 'write'],
      };
      await service.activateUserToApp(mockUser.id, mockApp.id, activationRequestWithPermissions, orgEnvId);

      expect(appService.requestPermissionsForUser).toHaveBeenCalledWith(orgEnvId, {
        userId: mockUser.id,
        appId: mockApp.id,
        permissions: activationRequestWithPermissions.permissions,
        parentEmail: mockParentEmail,
      });
      expect(requestPermissionsForUserSpy).toHaveBeenCalled();
      expect(activationSuccessSpy).toHaveBeenCalled();
    });

    it('triggers child-activated webhook', async () => {
      jest.spyOn(familyService, 'getGuardianLinks').mockResolvedValueOnce({
        pendingRequests: [
          {
            email: mockParentEmail,
          },
        ] as unknown as PendingGuardianLinkDTO[],
        confirmedRequests: [],
      });
      jest
        .spyOn(appService, 'requestPermissionsForUser')
        .mockResolvedValueOnce({ userSettings: [] } as unknown as ReturnType<
          typeof appService.requestPermissionsForUser
        >);
      const sendChildActivated = jest.spyOn(webhookService, 'sendChildActivated');

      await service.activateUserToApp(
        mockUser.id,
        mockApp.id,
        {
          permissions: ['read', 'write'],
        },
        mockCredentials.clientId,
      );

      expect(sendChildActivated).toHaveBeenCalled();
    });

    it('activates user to app without permissions', async () => {
      const requestPermissionsForUserSpy = jest.spyOn(appService, 'requestPermissionsForUser');
      const activationSuccessSpy = jest.spyOn(analyticsService, 'activationSuccess');

      const activationRequestNoPermissions: UserAppActivationDTO = {
        appName: 'test-app',
      };
      await service.activateUserToApp(
        mockUser.id,
        mockApp.id,
        activationRequestNoPermissions,
        mockCredentials.clientId,
      );

      expect(requestPermissionsForUserSpy).not.toHaveBeenCalled();
      expect(activationSuccessSpy).toHaveBeenCalled();
    });
  });

  describe('verifyPassword', () => {
    const username = 'testuser';
    const password = 'testpassword';
    const orgEnvId = '123';

    function setupUserWithPassword(passwordHash: string | undefined) {
      const user = new User();
      user.username = username;
      user.password = passwordHash;

      jest.spyOn(userRepo, 'findOneByOrFail').mockResolvedValueOnce(user);
      return user;
    }

    // eslint-disable-next-line unicorn/consistent-function-scoping
    function createKwsPasswordHash(testPassword: string, salt: string, iterations: number) {
      const calculatedHash = Buffer.from(crypto.pbkdf2Sync(testPassword, salt, iterations, 32, 'sha256')).toString(
        'base64',
      );

      return `pbkdf2_sha256$${iterations}$${salt}$${calculatedHash}`;
    }

    // eslint-disable-next-line unicorn/consistent-function-scoping
    function createPopJamPasswordHash(testPassword: string, salt: string, iterations: number) {
      const key = crypto.pbkdf2Sync(testPassword, salt, iterations, 64, 'sha1');
      const calculatedHash = key.toString('base64').slice(0, 86).replaceAll('/', '_').replaceAll('+', '-');

      return `pbkdf2_sha1$${iterations}$${salt}$${calculatedHash}`;
    }

    // eslint-disable-next-line unicorn/consistent-function-scoping
    function createSha1PasswordHash(testPassword: string, salt: string, iterations: number) {
      const calculatedHash = crypto
        .createHash('sha1')
        .update(testPassword + salt)
        .digest('hex')
        .toUpperCase();

      return `sha1$${iterations}$${salt}$${calculatedHash}`;
    }

    it('should throw BadRequestException if user has no password', async () => {
      setupUserWithPassword(void 0);
      await expect(service.verifyPassword(username, password, orgEnvId)).rejects.toThrow('User has no password');
    });

    it('should return false if password format is invalid', async () => {
      setupUserWithPassword('invalid_format');
      await expect(service.verifyPassword(username, password, orgEnvId)).resolves.toBe(false);
    });

    it('should return false if algorithm is not supported', async () => {
      setupUserWithPassword('unsupported_algo$10$salt$hash');
      await expect(service.verifyPassword(username, password, orgEnvId)).resolves.toBe(false);
    });

    // KWS password tests
    it('should verify KWS password correctly with right password', async () => {
      const salt = 'testsalt';
      const iterations = 10000;
      setupUserWithPassword(createKwsPasswordHash(password, salt, iterations));
      await expect(service.verifyPassword(username, password, orgEnvId)).resolves.toBe(true);
    });

    it('should verify KWS password correctly with wrong password', async () => {
      const salt = 'testsalt';
      const iterations = 10000;
      setupUserWithPassword(createKwsPasswordHash(password, salt, iterations));
      await expect(service.verifyPassword(username, 'wrongpassword', orgEnvId)).resolves.toBe(false);
    });

    // PopJam password tests
    it('should verify PopJam password correctly with right password', async () => {
      const salt = 'testsalt';
      const iterations = 10000;
      setupUserWithPassword(createPopJamPasswordHash(password, salt, iterations));
      await expect(service.verifyPassword(username, password, orgEnvId)).resolves.toBe(true);
    });

    it('should verify PopJam password correctly with wrong password', async () => {
      const salt = 'testsalt';
      const iterations = 10000;
      setupUserWithPassword(createPopJamPasswordHash(password, salt, iterations));
      await expect(service.verifyPassword(username, 'wrongpassword', orgEnvId)).resolves.toBe(false);
    });

    // SHA1 password tests
    it('should verify SHA1 password correctly with right password', async () => {
      const salt = 'testsalt';
      const iterations = 1;
      setupUserWithPassword(createSha1PasswordHash(password, salt, iterations));
      await expect(service.verifyPassword(username, password, orgEnvId)).resolves.toBe(true);
    });

    it('should verify SHA1 password correctly with wrong password', async () => {
      const salt = 'testsalt';
      const iterations = 1;
      setupUserWithPassword(createSha1PasswordHash(password, salt, iterations));
      await expect(service.verifyPassword(username, 'wrongpassword', orgEnvId)).resolves.toBe(false);
    });

    it('should return false for SHA1 with iterations not equal to 1', async () => {
      const salt = 'testsalt';
      const iterations = 2; // Should be 1 for SHA1, doesn't accept iterations > 1
      setupUserWithPassword(createSha1PasswordHash(password, salt, iterations));
      await expect(service.verifyPassword(username, password, orgEnvId)).resolves.toBe(false);
    });
  });

  describe('userActivatedForApp', () => {
    const userId = 123;
    const appId = 456;
    const orgEnvId = 'test-org-env';

    it('should return true when user is activated for app', async () => {
      const mockActivation = { userId, appId, orgEnvId } as Activation;
      jest.spyOn(activationRepo, 'findOne').mockResolvedValueOnce(mockActivation);

      const result = await service.userActivatedForApp(userId, appId, orgEnvId);

      expect(activationRepo.findOne).toHaveBeenCalledWith({
        where: { userId, orgEnvId, appId },
      });
      expect(result).toBe(true);
    });

    it('should return false when user is not activated for app', async () => {
      jest.spyOn(activationRepo, 'findOne').mockResolvedValueOnce(null);

      const result = await service.userActivatedForApp(userId, appId, orgEnvId);

      expect(activationRepo.findOne).toHaveBeenCalledWith({
        where: { userId, orgEnvId, appId },
      });
      expect(result).toBe(false);
    });
  });

  describe('isUsernameAvailable', () => {
    const orgEnvId = 'test-org-env';
    let userRepoFindOneSpy: SpyInstance;

    beforeEach(() => {
      userRepoFindOneSpy = jest.spyOn(userRepo, 'findOne');
    });

    it('should return false for empty username', async () => {
      await expect(service.isUsernameAvailable('', orgEnvId)).resolves.toBe(false);
      await expect(service.isUsernameAvailable('   ', orgEnvId)).resolves.toBe(false);
      expect(userRepoFindOneSpy).not.toHaveBeenCalled();
    });

    it('should return false when username exists', async () => {
      const username = 'existingUser';

      jest.spyOn(userRepo, 'findOne').mockResolvedValueOnce({ id: 1 } as User);

      const result = await service.isUsernameAvailable(username, orgEnvId);

      expect(result).toBe(false);
      expect(userRepoFindOneSpy).toHaveBeenCalled();
    });

    it('should return true when username does not exist', async () => {
      const username = 'newUser';

      jest.spyOn(userRepo, 'findOne').mockResolvedValueOnce(null);

      const result = await service.isUsernameAvailable(username, orgEnvId);

      expect(result).toBe(true);
      expect(userRepoFindOneSpy).toHaveBeenCalled();
    });
  });

  describe('createUserV2', () => {
    const mockUser = {
      id: 123,
      language: 'en',
      signUpCountry: 'US',
      username: 'testuser',
      password: 'hashedpassword',
      dateOfBirth: new Date('2015-01-01'),
      orgEnvId: 'test-org-env',
    } as User;

    const mockRegisterDTO = {
      username: 'testuser',
      password: 'testpassword',
      dateOfBirth: '2015-01-01',
      language: 'en',
      originAppId: 456,
    } as unknown as UserRegisterDTO;

    const mockAppInfo = {
      app: { id: 456, name: 'test-app' } as App,
      credentials: { clientId: 'client-id', secret: 'secret' },
      productId: 789,
    } as unknown as IAppInfo;

    const mockConsentAgeResponse = {
      consentAge: 16,
      location: 'US',
    } as unknown as AgeGateDTO;

    beforeEach(() => {
      jest.spyOn(appService, 'getAppInfo').mockResolvedValue(mockAppInfo);
      jest.spyOn(userRepo, 'save').mockResolvedValue(mockUser);
      jest.spyOn(ageGateService, 'getConsentAgeForCountry').mockResolvedValue(mockConsentAgeResponse);
    });

    it('should create a user successfully', async () => {
      const result = await service.createUserV2(mockRegisterDTO, 'US', 'test-org-env');

      expect(result).toBe(mockUser.id);
      expect(appService.getAppInfo).toHaveBeenCalledWith('test-org-env', 456);
      expect(service['ageGateService'].getConsentAgeForCountry).toHaveBeenCalledWith(
        {
          location: 'US',
          dob: '2015-01-01',
        },
        mockAppInfo.credentials,
      );
      expect(userRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          language: 'en',
          signUpCountry: 'US',
          username: 'testuser',
          password: expect.any(String),
          dateOfBirth: mockRegisterDTO.dateOfBirth,
          orgEnvId: 'test-org-env',
        }),
      );
    });

    it('should throw BadRequestException when user is not a minor', async () => {
      await expect(
        service.createUserV2(
          {
            ...mockRegisterDTO,
            dateOfBirth: '1990-05-13',
          },
          'US',
          'test-org-env',
        ),
      ).rejects.toThrow('User must be minor to register for an account or date of birth is invalid');
    });

    it('should throw BadRequestException when date of birth is invalid', async () => {
      await expect(
        service.createUserV2(
          {
            ...mockRegisterDTO,
            dateOfBirth: '1000-05-13',
          },
          'US',
          'test-org-env',
        ),
      ).rejects.toThrow('User must be minor to register for an account or date of birth is invalid');
    });
  });
});
