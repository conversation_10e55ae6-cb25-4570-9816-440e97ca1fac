import { NotFoundException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { AppUser, ResultPermission, UserCreateResponseDTO } from '@superawesome/freekws-classic-wrapper-common';
import { plainToInstance } from 'class-transformer';
import { v4 as uuidv4 } from 'uuid';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { SettingsService } from '../common/services/settings/settings.service';
import { Testing } from '../common/utils';
import { OrgEnv } from '../org-env/org-env.entity';
import { User } from '../user/user.entity';
import { UserService } from '../user/user.service';

describe('AppController', () => {
  let controller: AppController;
  let appService: AppService;
  let userService: UserService;
  const testOrgEnv = { id: uuidv4() } as OrgEnv;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      controllers: [AppController],
    });

    controller = module.get<AppController>(AppController);
    appService = module.get<AppService>(AppService);
    userService = module.get<UserService>(UserService);
  });

  describe('createUser', () => {
    it('should return mapped response without extra props', async () => {
      jest.spyOn(appService, 'registerUser').mockResolvedValueOnce({
        id: 1,
        uuid: '00000000-0000-0000-0000-000000000000',
        isMinor: true,
        permissions: {
          'chat.voice': true,
        },
        extraProp: 42,
      } as UserCreateResponseDTO);

      await expect(
        controller.createUser(
          1,
          {
            country: 'AF',
            dateOfBirth: '',
            language: 'ca',
            parentEmail: '<EMAIL>',
          },
          testOrgEnv,
        ),
      ).resolves.toEqual({
        id: 1,
        isMinor: true,
        permissions: {
          'chat.voice': true,
        },
        uuid: '00000000-0000-0000-0000-000000000000',
      });
    });
  });

  describe('updateUserPermissions', () => {
    it('should handle empty permissions by returning user data', async () => {
      const mockUser = {
        id: 1,
        dateOfBirth: '10-10-2012',
        language: 'en',
        signUpCountry: 'us',
        username: 'something',
      } as unknown as User;
      jest.spyOn(userService, 'getUser').mockResolvedValueOnce(mockUser);

      const result = await controller.updateUserPermissions(
        {
          appId: 1,
          userId: 1,
        },
        { permissions: [] },
        testOrgEnv,
      );

      const appUser = plainToInstance(AppUser, {
        id: 1,
        dateOfBirth: '10-10-2012',
        isDeleted: false,
        language: 'en',
        parentDeleted: false,
        parentEmail: undefined,
        parentExpired: false,
        parentIdVerified: false,
        parentRejected: false,
        parentVerified: false,
        signUpCountry: 'us',
        username: 'something',
      });

      expect(result).toEqual({
        user: appUser,
        permissions: undefined,
        resultPermissions: undefined,
      });
    });

    it('should throw NotFoundException when user not found and no permissions provided', async () => {
      jest.spyOn(userService, 'getUser').mockResolvedValueOnce(null);

      await expect(
        controller.updateUserPermissions(
          {
            appId: 1,
            userId: 1,
          },
          { permissions: [] },
          testOrgEnv,
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should attempt to activate user for Innersloth org env', async () => {
      const innerslothOrgEnv = { id: '8080e2b3-4f65-47b7-bd67-df01913a9a0d' } as OrgEnv;

      const mockUser = {
        id: 1,
        dateOfBirth: '10-10-2012',
        language: 'en',
        signUpCountry: 'us',
        username: 'something',
      } as unknown as User;

      jest.spyOn(userService, 'getUser').mockResolvedValueOnce(mockUser);
      const userActivatedSpy = jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(false);
      const activateUserSpy = jest.spyOn(userService, 'activateUserToApp').mockResolvedValueOnce({
        user: mockUser,
        activationPermissions: {},
      });

      await controller.updateUserPermissions(
        {
          appId: 1,
          userId: 1,
        },
        { permissions: [] },
        innerslothOrgEnv,
      );

      expect(userActivatedSpy).toHaveBeenCalledWith(1, 1, innerslothOrgEnv.id);
      expect(activateUserSpy).toHaveBeenCalledWith(1, 1, { permissions: [] }, innerslothOrgEnv.id);
    });

    it('should throw NotFoundException when user is not activated for provided app and permissions are provided', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(false);

      await expect(
        controller.updateUserPermissions(
          {
            appId: 1,
            userId: 1,
          },
          { permissions: ['somePermission'] },
          testOrgEnv,
        ),
      ).rejects.toThrow(new NotFoundException('User not activated for the provided app'));

      expect(userService.userActivatedForApp).toHaveBeenCalledWith(1, 1, testOrgEnv.id);
    });

    it('should return processed permissions data when permissions provided', async () => {
      const mockResponse = {
        user: {
          id: 1,
          language: 'en',
          parentEmail: '<EMAIL>',
          dateOfBirth: undefined,
          isDeleted: false,
          parentDeleted: false,
          parentExpired: false,
          parentIdVerified: false,
          parentRejected: false,
          parentVerified: false,
          signUpCountry: undefined,
          username: undefined,
        },
        userSettings: [{ namespace: 'default', settingName: 'permission1' }],
      } as unknown as ReturnType<typeof appService.requestPermissionsForUser>;

      const resultPermission: ResultPermission = {
        description: 'someDescription',
        displayName: 'someDisplayName',
        name: 'someName',
      };
      jest.spyOn(appService, 'requestPermissionsForUser').mockResolvedValueOnce(mockResponse);
      jest.spyOn(SettingsService, 'removeTermsAndConditionsSetting').mockReturnValueOnce([
        {
          namespace: 'permission1',
          settingName: 'granted',
        },
      ]);
      jest.spyOn(SettingsService, 'transformSettingsToGroupedPermissions').mockReturnValueOnce({
        alreadyGrantedPerms: [resultPermission],
        automaticallyGrantedPerms: [resultPermission],
        missingPermissions: [resultPermission],
      });
      jest.spyOn(SettingsService, 'transformSettingsToPermissions').mockReturnValueOnce({
        permission1: true,
      });
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(true);

      const result = await controller.updateUserPermissions(
        { appId: 1, userId: 1 },
        { permissions: ['permission1'] },
        testOrgEnv,
      );

      expect(result).toEqual({
        user: {
          id: 1,
          language: 'en',
          parentEmail: '[REDACTED]',
          dateOfBirth: undefined,
          isDeleted: false,
          parentDeleted: false,
          parentExpired: false,
          parentIdVerified: false,
          parentRejected: false,
          parentVerified: false,
          signUpCountry: undefined,
          username: undefined,
        },
        permissions: { permission1: true },
        resultPermissions: {
          alreadyGrantedPerms: [resultPermission],
          automaticallyGrantedPerms: [resultPermission],
          missingPermissions: [resultPermission],
        },
      });

      expect(appService.requestPermissionsForUser).toHaveBeenCalledWith(testOrgEnv.id, {
        appId: 1,
        userId: 1,
        permissions: ['permission1'],
      });
    });
  });

  describe('updateParentEmail', () => {
    it('should return void and call service', async () => {
      const spy = jest.spyOn(appService, 'updateParentEmail');
      await expect(
        controller.updateParentEmail({ appId: 1, userId: 1 }, { parentEmail: 'email' }, testOrgEnv),
      ).resolves.toBeUndefined();
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('getUser', () => {
    it('should return expected response', async () => {
      const spyGetUser = jest.spyOn(appService, 'getUser').mockResolvedValueOnce({
        id: 1,
        language: 'en',
        permissions: {},
        username: null,
        displayName: null,
        appData: [],
        activationCreatedAt: new Date('2024-10-10'),
        createdAt: new Date('2024-10-10'),
        consentAgeForCountry: 16,
        isMinor: true,
        parentEmail: '[REDACTED]',
        parentState: {
          verified: true,
          expired: false,
          rejected: false,
          idVerified: true,
          deleted: false,
        },
      });
      await expect(controller.getUser({ appId: 1, userId: 1 }, testOrgEnv)).resolves.toEqual({
        activationCreatedAt: '2024-10-10T00:00:00.000Z',
        appData: [],
        city: null,
        consentAgeForCountry: 16,
        createdAt: '2024-10-10T00:00:00.000Z',
        displayName: null,
        id: 1,
        isMinor: true,
        language: 'en',
        parentEmail: '[REDACTED]',
        parentState: {
          deleted: false,
          expired: false,
          idVerified: true,
          rejected: false,
          verified: true,
        },
        country: null,
        permissions: {},
        dateOfBirth: undefined,
        email: null,
        firstName: null,
        lastName: null,
        postalCode: null,
        streetAddress: null,
        username: null,
      });
      expect(spyGetUser).toHaveBeenCalled();
    });
  });

  describe('getUserPermissions', () => {
    it('should return permissions', async () => {
      const mockDate = new Date();
      const userId = 1;
      const appId = 2;
      const mockPermissions = {
        perm1: true,
        perm2: false,
      };

      jest.spyOn(appService, 'getUser').mockResolvedValueOnce({
        id: userId,
        isMinor: false,
        language: 'en',
        activationCreatedAt: mockDate,
        createdAt: mockDate,
        consentAgeForCountry: 18,
        permissions: mockPermissions,
      });

      const res = await controller.getUserPermissions('', appId, userId, testOrgEnv);

      expect(res).toEqual(mockPermissions);
    });

    it('should return extended permissions', async () => {
      const mockDate = new Date();
      const userId = 1;
      const appId = 2;
      const mockPermissions = {
        perm1: true,
        perm2: false,
      };
      const isMinor = false;

      jest.spyOn(appService, 'getUser').mockResolvedValueOnce({
        id: userId,
        isMinor: isMinor,
        language: 'en',
        activationCreatedAt: mockDate,
        createdAt: mockDate,
        consentAgeForCountry: 18,
        permissions: mockPermissions,
      });

      const res = await controller.getUserPermissions('true', appId, userId, testOrgEnv);

      expect(res).toEqual({
        permissions: mockPermissions,
        isGraduated: !isMinor,
        disabledPermissions: [],
      });
    });
  });

  describe('deleteUserSettings', () => {
    it('throws NotFound when user has no activation', async () => {
      const hasActivationsSpy = jest.spyOn(appService, 'userHasActivation').mockResolvedValueOnce(false);

      await expect(
        controller.deleteUserActivation(
          {
            appId: 1,
            userId: 1,
          },
          testOrgEnv,
        ),
      ).rejects.toThrow(NotFoundException);

      expect(hasActivationsSpy).toHaveBeenCalled();
    });

    it('calls deleteUserSettings when user has activation', async () => {
      const hasActivationsSpy = jest.spyOn(appService, 'userHasActivation').mockResolvedValueOnce(true);
      const deleteUserSettingsSpy = jest.spyOn(userService, 'deleteActivation').mockResolvedValueOnce();

      await expect(
        controller.deleteUserActivation(
          {
            appId: 1,
            userId: 1,
          },
          testOrgEnv,
        ),
      ).resolves.toBe(undefined);

      expect(hasActivationsSpy).toHaveBeenCalled();
      expect(deleteUserSettingsSpy).toHaveBeenCalled();
    });
  });

  describe('reviewPermissions should', () => {
    it('return no content', async () => {
      const reviewPermissionsSpy = jest.spyOn(appService, 'reviewPermissions').mockResolvedValueOnce();

      const result = await controller.reviewPermissions({ appId: 1, userId: 1 }, testOrgEnv);

      expect(result).toEqual({});
      expect(reviewPermissionsSpy).toHaveBeenCalled();
    });

    it('generate a consent request', async () => {
      const sendGenerateConsentRequest = jest.fn().mockResolvedValueOnce({});
      appService.reviewPermissions = sendGenerateConsentRequest;

      await controller.reviewPermissions({ appId: 1, userId: 1 }, testOrgEnv);

      expect(sendGenerateConsentRequest).toHaveBeenCalled();
    });
  });

  describe('activeUserToAnotherApp', () => {
    const mockUserId = 1;
    const mockAppId = 2;
    const mockOrgEnv = { id: 'test-org-env' } as OrgEnv;
    const mockPermissions = ['chat.voice', 'chat.text'];

    it('should successfully activate user to another app', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(false);
      jest.spyOn(userService, 'activateUserToApp').mockResolvedValueOnce({
        user: { id: mockUserId } as unknown as User,
        activationPermissions: {
          'chat.voice': true,
          'chat.text': false,
        },
      });

      const result = await controller.activeUserToAnotherApp(
        mockAppId,
        mockUserId,
        { permissions: mockPermissions },
        mockOrgEnv,
      );

      expect(result).toEqual({
        id: mockUserId,
        permissions: {
          'chat.voice': true,
          'chat.text': false,
        },
      });
      expect(userService.userActivatedForApp).toHaveBeenCalledWith(mockUserId, mockAppId, mockOrgEnv.id);
      expect(userService.activateUserToApp).toHaveBeenCalledWith(
        mockUserId,
        mockAppId,
        { permissions: mockPermissions },
        mockOrgEnv.id,
      );
    });

    it('should throw ConflictException when user is already activated for the app', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(true);
      const activateUserToAppSpy = jest.spyOn(userService, 'activateUserToApp');

      await expect(
        controller.activeUserToAnotherApp(mockAppId, mockUserId, { permissions: mockPermissions }, mockOrgEnv),
      ).rejects.toThrow('App already activated');

      expect(userService.userActivatedForApp).toHaveBeenCalledWith(mockUserId, mockAppId, mockOrgEnv.id);
      expect(activateUserToAppSpy).not.toHaveBeenCalled();
    });

    it('should handle activation without permissions', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(false);
      jest.spyOn(userService, 'activateUserToApp').mockResolvedValueOnce({
        user: { id: mockUserId } as unknown as User,
        activationPermissions: {},
      });

      const result = await controller.activeUserToAnotherApp(mockAppId, mockUserId, { permissions: [] }, mockOrgEnv);

      expect(result).toEqual({
        id: mockUserId,
        permissions: {},
      });
      expect(userService.userActivatedForApp).toHaveBeenCalledWith(mockUserId, mockAppId, mockOrgEnv.id);
      expect(userService.activateUserToApp).toHaveBeenCalledWith(
        mockUserId,
        mockAppId,
        { permissions: [] },
        mockOrgEnv.id,
      );
    });
  });
});
