import { BadRequestException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AgeGateDTO } from '@superawesome/freekws-agegate-api-common';
import { EncryptionService } from '@superawesome/freekws-auth-library';
import {
  EOAuthGrantType,
  EOAuthScope,
  OAuthAuthorisePayloadDTO,
  ParentState,
} from '@superawesome/freekws-classic-wrapper-common';
import { Repository } from 'typeorm';

import { JWKService } from './jwk.service';
import { OauthService, secondsInADay } from './oauth.service';
import { RefreshToken } from './refresh-token.entity';
import { JwtUserPayload, TJWT } from './types';
import { App } from '../app/app.entity';
import { IAppOauthClient } from '../app/types';
import { AgeGateService } from '../common/services/age-gate/age-gate.service';
import { SettingsService } from '../common/services/settings/settings.service';
import { Testing } from '../common/utils';
import { OrgEnv } from '../org-env/org-env.entity';
import { User } from '../user/user.entity';
import { UserService } from '../user/user.service';

describe('OauthService', () => {
  let service: OauthService;
  let jwkService: JWKService;
  let settingsService: SettingsService;
  let userService: UserService;
  let ageGateService: AgeGateService;
  let encryptionService: EncryptionService;
  let refreshTokenRepo: Repository<RefreshToken>;
  let userRepo: Repository<User>;
  let appRepo: Repository<App>;
  let orgEnvRepo: Repository<OrgEnv>;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [OauthService],
    });

    service = module.get<OauthService>(OauthService);
    jwkService = module.get<JWKService>(JWKService);
    settingsService = module.get<SettingsService>(SettingsService);
    userService = module.get<UserService>(UserService);
    ageGateService = module.get<AgeGateService>(AgeGateService);

    refreshTokenRepo = module.get(getRepositoryToken(RefreshToken));
    userRepo = module.get(getRepositoryToken(User));
    appRepo = module.get(getRepositoryToken(App));
    orgEnvRepo = module.get(getRepositoryToken(OrgEnv));
    encryptionService = module.get<EncryptionService>(EncryptionService);

    jest.clearAllMocks();
  });

  describe('getAccessToken', () => {
    it('should return token for APP scope', async () => {
      jest.spyOn(jwkService, 'sign').mockResolvedValueOnce('signed-token');

      await expect(
        service.getAccessToken(
          {
            scope: EOAuthScope.APP,
            client_id: 'client-id',
            client_secret: '',
            grant_type: EOAuthGrantType.CLIENT_CREDENTIALS,
          },
          { appId: 123456, clientId: 'client-id', orgEnvId: 'org' },
        ),
      ).resolves.toEqual('signed-token');
    });

    it('should throw an error for invalid scope', async () => {
      await expect(
        service.getAccessToken(
          {
            scope: '' as EOAuthScope,
            client_id: 'client-id',
            client_secret: '',
            grant_type: EOAuthGrantType.CLIENT_CREDENTIALS,
          },
          { appId: 123456, clientId: 'client-id', orgEnvId: 'org' },
        ),
      ).rejects.toThrow('Invalid `scope` value');
    });
  });

  describe('getRefreshToken', () => {
    const client: IAppOauthClient = {
      appId: 123,
      clientId: 'test-client-id',
      orgEnvId: 'org-env-123',
    };

    const mockRefreshToken = {
      token: 'test-refresh-token',
      id: 1,
      orgEnvId: 'org-env-123',
      appId: 123,
      clientId: 'test-client-id',
      scope: EOAuthScope.APP,
      expires: new Date(),
    };

    beforeEach(() => {
      jest.spyOn(refreshTokenRepo, 'save').mockResolvedValue(mockRefreshToken);
    });

    it('should create a refresh token without username', async () => {
      const createRefreshTokenSpy = jest.spyOn(service, 'createRefreshToken');
      const userRepoFindOrFailSpy = jest.spyOn(userRepo, 'findOneByOrFail');

      await service.getRefreshToken(EOAuthScope.APP, client);

      expect(userRepoFindOrFailSpy).not.toHaveBeenCalled();
      expect(createRefreshTokenSpy).toHaveBeenCalledWith({
        appId: client.appId,
        orgEnvId: client.orgEnvId,
        clientId: client.clientId,
        scope: EOAuthScope.APP,
        userId: undefined,
      });
    });

    it('should create a refresh token with username', async () => {
      const username = 'testuser';
      const userId = 456;
      const createRefreshTokenSpy = jest.spyOn(service, 'createRefreshToken');

      jest.spyOn(userRepo, 'findOneByOrFail').mockResolvedValue({ id: userId } as User);

      await service.getRefreshToken(EOAuthScope.USER, client, username);

      expect(userRepo.findOneByOrFail).toHaveBeenCalledWith({
        username,
        orgEnvId: client.orgEnvId,
      });

      expect(createRefreshTokenSpy).toHaveBeenCalledWith({
        appId: client.appId,
        orgEnvId: client.orgEnvId,
        clientId: client.clientId,
        scope: EOAuthScope.USER,
        userId,
      });
    });

    it('should handle undefined scope', async () => {
      const createRefreshTokenSpy = jest.spyOn(service, 'createRefreshToken');

      await service.getRefreshToken(undefined, client);

      expect(createRefreshTokenSpy).toHaveBeenCalledWith({
        appId: client.appId,
        orgEnvId: client.orgEnvId,
        clientId: client.clientId,
        scope: '',
        userId: undefined,
      });
    });
  });

  describe('createRefreshToken', () => {
    const refreshTokenData = {
      appId: 123,
      orgEnvId: 'org-env-123',
      clientId: 'test-client-id',
      scope: EOAuthScope.APP,
    };

    it('should create a refresh token', async () => {
      const now = Date.now();
      jest.spyOn(Date, 'now').mockReturnValue(now);

      const mockGeneratedToken = 'mocked-random-token';
      const expiresAt = new Date(now + 1000 * secondsInADay);

      jest.spyOn(refreshTokenRepo, 'save').mockResolvedValue({
        ...refreshTokenData,
        token: mockGeneratedToken,
        expires: expiresAt,
        id: 1,
      });

      const result = await service.createRefreshToken(refreshTokenData);

      expect(refreshTokenRepo.save).toHaveBeenCalledWith({
        ...refreshTokenData,
        token: expect.any(String),
        expires: expiresAt,
      });

      expect(result).toEqual({
        ...refreshTokenData,
        token: expect.any(String),
        expires: expiresAt,
        id: 1,
      });
    });
  });

  describe('getAccessTokenFromRefreshToken', () => {
    const refreshToken = 'valid-refresh-token';
    const client: IAppOauthClient = {
      appId: 123,
      clientId: 'test-client-id',
      orgEnvId: 'org-env-123',
    };
    const data = {
      grant_type: EOAuthGrantType.REFRESH_TOKEN,
      refresh_token: refreshToken,
      client_id: client.clientId,
      client_secret: 'test-client-secret',
    };

    it('should throw BadRequestException when refresh token is not found', async () => {
      jest.spyOn(refreshTokenRepo, 'findOne').mockResolvedValue(null);

      await expect(service.getAccessTokenFromRefreshToken(refreshToken, data, client)).rejects.toThrow(
        new BadRequestException('Invalid refresh token'),
      );

      expect(refreshTokenRepo.findOne).toHaveBeenCalledWith({
        where: {
          orgEnvId: client.orgEnvId,
          appId: client.appId,
          token: refreshToken,
        },
      });
    });

    it('should throw BadRequestException when refresh token is expired', async () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      jest.spyOn(refreshTokenRepo, 'findOne').mockResolvedValue({
        token: refreshToken,
        expires: yesterday,
      } as RefreshToken);

      await expect(service.getAccessTokenFromRefreshToken(refreshToken, data, client)).rejects.toThrow(
        new BadRequestException('Refresh token expired'),
      );
    });

    it('should throw BadRequestException when user token has no userId', async () => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      jest.spyOn(refreshTokenRepo, 'findOne').mockResolvedValue({
        token: refreshToken,
        expires: tomorrow,
        scope: EOAuthScope.USER,
        userId: undefined,
      } as RefreshToken);

      await expect(service.getAccessTokenFromRefreshToken(refreshToken, data, client)).rejects.toThrow(
        new BadRequestException('Corrupt refresh token'),
      );
    });

    it('should create user token when refresh token has valid userId', async () => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const userId = 456;

      jest.spyOn(refreshTokenRepo, 'findOne').mockResolvedValue({
        token: refreshToken,
        expires: tomorrow,
        scope: EOAuthScope.USER,
        userId,
      } as RefreshToken);

      const createUserTokenFromUserIdSpy = jest
        .spyOn(service, 'createUserTokenFromUserId')
        .mockResolvedValue('new-user-token');

      const result = await service.getAccessTokenFromRefreshToken(refreshToken, data, client);

      expect(createUserTokenFromUserIdSpy).toHaveBeenCalledWith(userId, client);
      expect(result).toBe('new-user-token');
    });

    it('should create non-user token when refresh token is not for a user', async () => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      jest.spyOn(refreshTokenRepo, 'findOne').mockResolvedValue({
        token: refreshToken,
        expires: tomorrow,
        scope: EOAuthScope.APP,
      } as RefreshToken);

      const getAccessTokenSpy = jest.spyOn(service, 'getAccessToken').mockResolvedValue('new-app-token');

      const result = await service.getAccessTokenFromRefreshToken(refreshToken, data, client);

      expect(getAccessTokenSpy).toHaveBeenCalledWith(data, client);
      expect(result).toBe('new-app-token');
    });
  });

  describe('createUserTokenFromUserId', () => {
    const client: IAppOauthClient = {
      appId: 123,
      clientId: 'test-client-id',
      orgEnvId: 'org-env-123',
    };
    const userId = 456;
    const mockUser = {
      id: userId,
      orgEnvId: client.orgEnvId,
      signUpCountry: 'US',
      dateOfBirth: new Date('2000-01-01'),
    } as User;

    const mockApp = {
      id: client.appId,
      productId: 'product-123',
      orgEnvId: client.orgEnvId,
    } as App;

    const mockOrgEnv = {
      id: client.orgEnvId,
      clientId: 'org-client-id',
      clientSecret: 'org-client-secret',
      orgId: 'org-123',
    } as OrgEnv;

    beforeEach(() => {
      jest.spyOn(userRepo, 'findOneByOrFail').mockResolvedValue(mockUser);
      jest.spyOn(appRepo, 'findOneByOrFail').mockResolvedValue(mockApp);
      jest.spyOn(orgEnvRepo, 'findOneByOrFail').mockResolvedValue(mockOrgEnv);
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValue([]);
      jest.spyOn(userService, 'getParentEmail').mockResolvedValue('<EMAIL>');
      jest.spyOn(userService, 'getParentState').mockResolvedValue({ idVerified: true } as ParentState);
      jest.spyOn(ageGateService, 'getConsentAgeForCountry').mockResolvedValue({
        underAgeOfDigitalConsent: true,
      } as AgeGateDTO);
      jest.spyOn(jwkService, 'sign').mockResolvedValue('signed-user-token');
    });

    it('should create a user token with all necessary data', async () => {
      const result = await service.createUserTokenFromUserId(userId, client);

      expect(userRepo.findOneByOrFail).toHaveBeenCalledWith({
        orgEnvId: client.orgEnvId,
        id: userId,
      });

      expect(jwkService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: mockUser.id,
          appId: client.appId,
          clientId: mockApp.oauthClientId,
          scope: EOAuthScope.USER,
          signUpCountry: mockUser.signUpCountry,
          parentEmail: '<EMAIL>',
          parentVerified: true,
          parentState: expect.any(Object),
          isMinor: true,
        }),
        client.orgEnvId,
      );

      expect(result).toBe('signed-user-token');
    });

    it('should handle missing parent email', async () => {
      jest.spyOn(userService, 'getParentEmail').mockResolvedValueOnce(void 0);

      const result = await service.createUserTokenFromUserId(userId, client);

      expect(userService.getParentState).not.toHaveBeenCalled();
      expect(jwkService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          parentEmail: undefined,
          parentVerified: false,
          parentState: {
            idVerified: false,
            deleted: false,
            expired: false,
            rejected: false,
            verified: false,
          },
        }),
        client.orgEnvId,
      );
      expect(result).toBe('signed-user-token');
    });
  });

  describe('getRefreshTokenWithUserId', () => {
    const client: IAppOauthClient = {
      appId: 123,
      clientId: 'test-client-id',
      orgEnvId: 'org-env-123',
    };

    const mockRefreshToken = {
      token: 'test-refresh-token',
      id: 1,
      orgEnvId: 'org-env-123',
      appId: 123,
      clientId: 'test-client-id',
      scope: EOAuthScope.USER,
      userId: 456,
      expires: new Date(),
    };

    beforeEach(() => {
      jest.spyOn(refreshTokenRepo, 'save').mockResolvedValue(mockRefreshToken);
    });

    it('should create a refresh token with userId', async () => {
      const userId = 456;
      const createRefreshTokenSpy = jest.spyOn(service, 'createRefreshToken');

      const result = await service.getRefreshTokenWithUserId(userId, client);

      expect(createRefreshTokenSpy).toHaveBeenCalledWith({
        appId: client.appId,
        orgEnvId: client.orgEnvId,
        clientId: client.clientId,
        scope: EOAuthScope.USER,
        userId,
      });
      expect(result).toEqual(mockRefreshToken);
    });
  });

  describe('createUserTokenFromUsername', () => {
    const client: IAppOauthClient = {
      appId: 123,
      clientId: 'test-client-id',
      orgEnvId: 'org-env-123',
    };
    const username = 'testuser';
    const mockUser = {
      id: 456,
      orgEnvId: client.orgEnvId,
      username,
      signUpCountry: 'US',
      dateOfBirth: new Date('2000-01-01'),
    } as User;

    const mockApp = {
      id: client.appId,
      productId: 'product-123',
      orgEnvId: client.orgEnvId,
    } as App;

    const mockOrgEnv = {
      id: client.orgEnvId,
      clientId: 'org-client-id',
      clientSecret: 'org-client-secret',
      orgId: 'org-123',
    } as OrgEnv;

    beforeEach(() => {
      jest.spyOn(userRepo, 'findOneByOrFail').mockResolvedValue(mockUser);
      jest.spyOn(appRepo, 'findOneByOrFail').mockResolvedValue(mockApp);
      jest.spyOn(orgEnvRepo, 'findOneByOrFail').mockResolvedValue(mockOrgEnv);
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValue([]);
      jest.spyOn(userService, 'getParentEmail').mockResolvedValue('<EMAIL>');
      jest.spyOn(userService, 'getParentState').mockResolvedValue({ idVerified: true } as ParentState);
      jest.spyOn(ageGateService, 'getConsentAgeForCountry').mockResolvedValue({
        underAgeOfDigitalConsent: true,
      } as AgeGateDTO);
      jest.spyOn(jwkService, 'sign').mockResolvedValue('signed-user-token');
    });

    it('should create a user token with all necessary data', async () => {
      const result = await service.createUserTokenFromUsername(client, username);

      expect(userRepo.findOneByOrFail).toHaveBeenCalledWith({
        orgEnvId: client.orgEnvId,
        username,
      });

      expect(jwkService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: mockUser.id,
          appId: client.appId,
          clientId: mockApp.oauthClientId,
          scope: EOAuthScope.USER,
          signUpCountry: mockUser.signUpCountry,
          parentEmail: '<EMAIL>',
          parentVerified: true,
          parentState: expect.any(Object),
          isMinor: true,
        }),
        client.orgEnvId,
      );

      expect(result).toBe('signed-user-token');
    });

    it('should handle user without date of birth', async () => {
      const userWithoutDob = { ...mockUser, dateOfBirth: undefined } as User;
      jest.spyOn(userRepo, 'findOneByOrFail').mockResolvedValue(userWithoutDob);

      const result = await service.createUserTokenFromUsername(client, username);

      expect(ageGateService.getConsentAgeForCountry).toHaveBeenCalledWith(
        {
          location: mockUser.signUpCountry,
          dob: undefined,
        },
        expect.any(Object),
      );
      expect(result).toBe('signed-user-token');
    });
  });
  describe('getAuthCode', () => {
    const mockJwt = {
      appId: 123,
      userId: 456,
      clientId: 'test-client-id',
    } as JwtUserPayload;

    const mockOrgEnv = {
      id: 'test-org-env',
    } as OrgEnv;

    const mockApp = {
      id: mockJwt.appId,
      oauthCallback: 'https://test.com/callback',
    } as App;

    beforeEach(() => {
      jest.spyOn(appRepo, 'findOneByOrFail').mockResolvedValue(mockApp);
      jest.spyOn(jwkService, 'sign').mockResolvedValue('signed-auth-code');
      jest.spyOn(encryptionService, 'encrypt').mockReturnValue('encrypted-value');
    });

    it('should create auth code when redirect URI matches', async () => {
      const data = {
        redirect_uri: 'https://test.com/callback',
      } as OAuthAuthorisePayloadDTO;

      const result = await service.getAuthCode(data, mockJwt, mockOrgEnv);

      expect(jwkService.sign).toHaveBeenCalledWith(
        {
          scope: EOAuthScope.USER,
          userId: mockJwt.userId,
          clientId: mockJwt.clientId,
        },
        mockOrgEnv.id,
        120,
      );
      expect(result).toBe('signed-auth-code');
    });

    it('should throw BadRequestException when redirect URI does not match', async () => {
      const data = {
        redirect_uri: 'https://wrong.com/callback',
      } as OAuthAuthorisePayloadDTO;

      await expect(service.getAuthCode(data, mockJwt, mockOrgEnv)).rejects.toThrow(
        new BadRequestException('Invalid or missing redirect_uri parameter'),
      );
    });

    it('should include code challenge when provided', async () => {
      const data = {
        redirect_uri: 'https://test.com/callback',
        code_challenge: 'test-challenge',
        code_challenge_method: 'S256',
      } as OAuthAuthorisePayloadDTO;

      await service.getAuthCode(data, mockJwt, mockOrgEnv);

      expect(encryptionService.encrypt).toHaveBeenCalledWith('test-challenge');
      expect(encryptionService.encrypt).toHaveBeenCalledWith('S256');
      expect(jwkService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          codeChallenge: 'encrypted-value',
          codeChallengeMethod: 'encrypted-value',
        }),
        mockOrgEnv.id,
        120,
      );
    });
  });

  describe('getJwt', () => {
    it('should verify JWT with correct parameters', async () => {
      const code = 'test-code';
      const orgEnvId = 'test-org-env';
      jest.spyOn(jwkService, 'verify').mockResolvedValue({ someData: true } as unknown as TJWT);

      const result = await service.getJwt(code, orgEnvId);

      expect(jwkService.verify).toHaveBeenCalledWith(code, orgEnvId);
      expect(result).toEqual({ someData: true });
    });
  });

  describe('verifyCode', () => {
    const mockApp = {
      id: 123,
      oauthCallback: 'https://test.com/callback',
    } as App;

    const verifyData = {
      appId: 123,
      clientId: 'test-client-id',
      redirect_uri: 'https://test.com/callback',
      codeJwt: {
        clientId: 'test-client-id',
      } as TJWT,
      codeVerifier: undefined,
    };

    beforeEach(() => {
      jest.spyOn(appRepo, 'findOneByOrFail').mockResolvedValue(mockApp);
      jest.spyOn(encryptionService, 'decrypt').mockReturnValue('decrypted-value');
    });

    it('should throw BadRequestException when client IDs do not match', async () => {
      const data = {
        ...verifyData,
        clientId: 'different-client-id',
      };

      await expect(service.verifyCode(data, 'test-org-env')).rejects.toThrow(
        new BadRequestException('Client ID mismatch between JWT and request'),
      );
    });

    it('should throw BadRequestException when redirect URIs do not match', async () => {
      const data = {
        ...verifyData,
        redirect_uri: 'https://wrong.com/callback',
      };

      await expect(service.verifyCode(data, 'test-org-env')).rejects.toThrow(
        new BadRequestException('Redirect URI mismatch between app and request'),
      );
    });

    it('should throw BadRequestException when code verifier is missing', async () => {
      const data = {
        ...verifyData,
        codeJwt: {
          ...verifyData.codeJwt,
          codeChallenge: 'challenge',
          codeChallengeMethod: 'method',
        },
      };

      await expect(service.verifyCode(data, 'test-org-env')).rejects.toThrow(
        new BadRequestException('Code contains code_challenge but request contains no code_verifier'),
      );
    });

    it('should verify plain code challenge', async () => {
      const data = {
        ...verifyData,
        codeVerifier: 'test-verifier',
        codeJwt: {
          ...verifyData.codeJwt,
          codeChallenge: 'encrypted-challenge',
          codeChallengeMethod: 'encrypted-method',
        },
      };

      jest
        .spyOn(encryptionService, 'decrypt')
        .mockReturnValueOnce('test-verifier') // challenge
        .mockReturnValueOnce('plain'); // method

      const result = await service.verifyCode(data, 'test-org-env');

      expect(result).toBe(true);
    });

    it('should verify S256 code challenge', async () => {
      const data = {
        ...verifyData,
        codeVerifier: 'test-verifier',
        codeJwt: {
          ...verifyData.codeJwt,
          codeChallenge: 'encrypted-challenge',
          codeChallengeMethod: 'encrypted-method',
        },
      };

      jest
        .spyOn(encryptionService, 'decrypt')
        .mockReturnValueOnce('gdWEFGfdgewrf434r3=') // challenge
        .mockReturnValueOnce('S256'); // method

      const result = await service.verifyCode(data, 'test-org-env');

      expect(result).toBe(false); // Will be false since we're not actually hashing
    });

    it('should throw BadRequestException for invalid code challenge method', async () => {
      const data = {
        ...verifyData,
        codeVerifier: 'test-verifier',
        codeJwt: {
          ...verifyData.codeJwt,
          codeChallenge: 'encrypted-challenge',
          codeChallengeMethod: 'encrypted-method',
        },
      };

      jest.spyOn(encryptionService, 'decrypt').mockReturnValueOnce('challenge').mockReturnValueOnce('invalid-method');

      await expect(service.verifyCode(data, 'test-org-env')).rejects.toThrow(
        new BadRequestException('Invalid code_challenge_method within code. Must be one of "plain" or "S256"'),
      );
    });

    it('should return true when no code challenge is present', async () => {
      const result = await service.verifyCode(verifyData, 'test-org-env');

      expect(result).toBe(true);
    });
  });
});
