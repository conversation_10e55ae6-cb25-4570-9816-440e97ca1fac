import http from 'k6/http';
import { check } from 'k6';
import {
    TARGET_TIME_CREATION_REQUEST,
    TARGET_TIME_OTHER_REQUEST
} from './sequential-scenarios-test';

const BASE_URL = __ENV.BASE_URL || 'http://localhost:7001';

const DEFAULT_HEADERS = {
    'x-forwarded-host': 'test.com',
    'Content-Type': 'application/json'
};

function addAuthHeader(token: string) {
    return {
        ...DEFAULT_HEADERS,
        'Authorization': `Bearer ${token}`
    };
}

function generateRandomString(): string {
    return Math.random().toString(36).slice(2, 15);
}

function processPayload(payload: any): any {
    const payloadStr = JSON.stringify(payload);
    return JSON.parse(payloadStr.replace(/{random}/g, generateRandomString()));
}

export function getOAuthToken(clientId: string, clientSecret: string): string | null {
    const payload = {
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: 'client_credentials',
        scope: 'app'
    };

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'x-forwarded-host': 'test.com'
    };

    const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

    if (response.status === 200) {
        return response.json('access_token') as string;
    }

    console.error(`Failed to get OAuth token: ${response.status} ${response.body}`);
    return null;
}

export function getOAuthUserToken(username: string, password: string) {
    const payload = {
        username,
        password,
        client_id: __ENV.CLIENT_ID,
        client_secret: __ENV.CLIENT_SECRET,
        grant_type: 'password',
        scope: 'user'
    };

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'x-forwarded-host': 'test.com'
    };

    const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

    if (response.status === 200) {
        const accessToken = response.json('access_token');
        return accessToken as string;
    }

    console.error(`Failed to get OAuth token: ${response.status} ${response.body}`);
    return null;
}

const JAVI_TEST_APP_ID = '207800543';
const DOM_CRAFT_APP_ID = '325570412';
const TEST_USER_IDS = [
    "747000751", "686380514", "926763521", "704956097", "948360665",
    "884187412", "872530714"
];

function getRandomUserId(): string {
    return TEST_USER_IDS[Math.floor(Math.random() * TEST_USER_IDS.length)];
}

const TEST_PASSWORD = "test-password";


function createFreshUserV2(token: string) {
    const payload = processPayload({
        username: "load-test-{random}",
        password: TEST_PASSWORD,
        parentEmail: "load-test-{random}@epicgames.com",
        originAppId: parseInt(JAVI_TEST_APP_ID),
        token: "test-token",
        dateOfBirth: "2022-05-13"
    });

    const headers = {
        ...addAuthHeader(token),
        "x-kws-bypass-talon": "true"
    };

    const response = http.post(`${BASE_URL}/v2/users`, JSON.stringify(payload), { headers });

    if (response.status === 201) {
        const user = response.json() as any;
        return { id: user.id, username: payload.username } as { id: number, username: string };
    }

    console.error(`Failed to create fresh user: ${response.status} ${response.body}`);
    return null;
}

function createFreshUserV2Apps(token: string) {
    const payload = processPayload({
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'load-test-fresh-user-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
    });

    const headers = addAuthHeader(token);
    const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users`, JSON.stringify(payload), { headers });

    if (response.status === 201) {
        const user = response.json() as any;
        return { id: user.id, username: payload.username } as { id: number, username: string };
    }

    console.error(`Failed to create fresh user: ${response.status} ${response.body}`);
    return null;
}

// OAuth test constants - fill in actual values as needed
const OAUTH_TEST_CONSTANTS = {

    // For authorization code flow
    TEST_REDIRECT_URI: 'https://example.com/callback',
    TEST_AUTHORIZATION_CODE: 'test-auth-code-replace-me',
    TEST_CODE_VERIFIER: 'test-code-verifier-replace-me',
};

export const requests = {
    // OAuth Flow 1: Client Credentials (App Authentication)
    oauthTokenClientCredentials: () => {
        const payload = {
            client_id: __ENV.CLIENT_ID,
            client_secret: __ENV.CLIENT_SECRET,
            grant_type: 'client_credentials',
            scope: 'app'
        };

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': 'test.com'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

        return check(response, {
            'oauth-client-credentials status is 200': (r) => r.status === 200,
            'oauth-client-credentials has access_token': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.access_token !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-client-credentials has refresh_token': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.refresh_token !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-client-credentials response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
        });
    },

    // OAuth Flow 1b: Client Credentials for DomCraft App
    oauthTokenClientCredentialsDomCraft: () => {
        const payload = {
            client_id: __ENV.DOM_CRAFT_CLIENT_ID,
            client_secret: __ENV.DOM_CRAFT_CLIENT_SECRET,
            grant_type: 'client_credentials',
            scope: 'app'
        };

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': 'test.com'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

        return check(response, {
            'oauth-domcraft-client-credentials status is 200': (r) => r.status === 200,
            'oauth-domcraft-client-credentials has access_token': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.access_token !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-domcraft-client-credentials response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
        });
    },

    // OAuth Flow 2: Password Grant (User Authentication)
    oauthTokenPassword: () => {
        const payload = {
            client_id: __ENV.CLIENT_ID,
            client_secret: __ENV.CLIENT_SECRET,
            grant_type: 'password',
            scope: 'user',
            username: "Penny",
            password: "BigBreakaway"
        };

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': 'test.com'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

        return check(response, {
            'oauth-password status is 200 or 401': (r) => r.status === 200,
            'oauth-password response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
            'oauth-password has proper response': (r) => {
                if (r.status === 200) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.access_token !== undefined && body.refresh_token !== undefined;
                    } catch {
                        return false;
                    }
                } else if (r.status === 401) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.error !== undefined;
                    } catch {
                        return false;
                    }
                }
                return false;
            },
        });
    },

    // OAuth Flow 3: Refresh Token Grant
    oauthTokenRefreshToken: () => {
        const refreshToken = "4eef37e5ff91f8d3b480c0ed5876679d9cc893dd3404d380109bb60f367defd2"
        const payload = {
            client_id: __ENV.CLIENT_ID,
            client_secret: __ENV.CLIENT_SECRET,
            grant_type: 'refresh_token',
            refresh_token: refreshToken
        };

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': 'test.com'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

        return check(response, {
            'oauth-refresh-token status is 200 or 400': (r) => r.status === 200 || r.status === 400, // 400 expected if refresh token is invalid
            'oauth-refresh-token response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
            'oauth-refresh-token has proper response': (r) => {
                if (r.status === 200) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.access_token !== undefined && body.refresh_token !== undefined;
                    } catch {
                        return false;
                    }
                }
                return false;
            },
        });
    },

    // OAuth Flow 4: Authorization Code Grant (requires prior /oauth/authorize call)
    oauthTokenAuthorizationCode: () => {
        const payload = {
            client_id: __ENV.CLIENT_ID,
            client_secret: __ENV.CLIENT_SECRET,
            grant_type: 'authorization_code',
            code: OAUTH_TEST_CONSTANTS.TEST_AUTHORIZATION_CODE,
            redirect_uri: OAUTH_TEST_CONSTANTS.TEST_REDIRECT_URI,
            code_verifier: OAUTH_TEST_CONSTANTS.TEST_CODE_VERIFIER
        };

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': 'test.com'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

        return check(response, {
            'oauth-authorization-code status is 200 or 400': (r) => r.status === 200 || r.status === 400, // 400 expected if code is invalid
            'oauth-authorization-code response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
            'oauth-authorization-code has proper response': (r) => {
                if (r.status === 200) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.access_token !== undefined && body.refresh_token !== undefined;
                    } catch {
                        return false;
                    }
                } else if (r.status === 400) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.error !== undefined;
                    } catch {
                        return false;
                    }
                }
                return false;
            },
        });
    },

    healthcheck: () => {
        const response = http.get(`${BASE_URL}/healthcheck`, { headers: DEFAULT_HEADERS });
        return check(response, {
            'healthcheck status is 200': (r) => r.status === 200,
            "health check response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    getJwks: () => {
        const response = http.get(`${BASE_URL}/v1/jwks`, { headers: DEFAULT_HEADERS });
        return check(response, {
            'get-jwks status is 200': (r) => r.status === 200,
            "get-jwks response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    childAge: () => {
        const response = http.get(`${BASE_URL}/v1/countries/child-age?country=GB&dob=2015-12-12`, { headers: DEFAULT_HEADERS });
        return check(response, {
            'child-age status is 200': (r) => r.status === 200,
            "child-age response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    checkUsername: () => {
        const username = `TestUser${generateRandomString().slice(0, 5)}`;
        const response = http.get(`${BASE_URL}/v1/users/check-username?username=${username}`, { headers: DEFAULT_HEADERS });
        return check(response, {
            'check-username status is 200': (r) => r.status === 200,
            "check-username response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    createUser: (token: string) => {
        const payload = processPayload({
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'load-test-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        });

        const headers = addAuthHeader(token);
        const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users`, JSON.stringify(payload), { headers });
        return check(response, {
            'create-user status is 201': (r) => r.status === 201,
            "create-user response time": (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
        });
    },

    createUserV2: (token: string) => {
        const payload = processPayload({
            username: "load-test-{random}",
            password: TEST_PASSWORD,
            parentEmail: "load-test-{random}@epicgames.com",
            originAppId: parseInt(JAVI_TEST_APP_ID),
            token: "test-token",
            dateOfBirth: "2022-05-13"
        });

        const headers = {
            ...addAuthHeader(token),
            "x-kws-bypass-talon": "true"
        };

        const response = http.post(`${BASE_URL}/v2/users`, JSON.stringify(payload), { headers });
        return check(response, {
            'create-user-v2 status is 201': (r) => r.status === 201,
            "create-user-v2 response time": (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
        });
    },

    getUser: (token: string) => {
        const userId = getRandomUserId();
        const headers = addAuthHeader(token);
        const response = http.get(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${userId}`, { headers });
        return check(response, {
            'get-user status is 200': (r) => r.status === 200,
            "get-user response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    activateUser: (token: string) => {
        const user = createFreshUserV2(token);

        if (!user) {
            console.error('Failed to create fresh user for activateUser');
            return false;
        }

        const payload = {
            appName: 'javi-test',
            permissions: ['Stranger.things', 'Greece.Opt-out'],
            dateOfBirth: '2010-01-01',
            parentEmail: processPayload('load-test-activate-{random}@epicgames.com')
        };

        const userToken = getOAuthUserToken(user.username, TEST_PASSWORD);
        if (!userToken) {
            console.error('Failed to get user token for activateUser');
            return false;
        }
        const headers = addAuthHeader(userToken);
        const response = http.post(`${BASE_URL}/v1/users/${user.id}/apps`, JSON.stringify(payload), { headers });
        return check(response, {
            'activate-user status is 200 or 201': (r) => r.status === 200 || r.status === 201,
            'activate-user has fresh user': () => user !== undefined,
            "activate-user response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    activateUserV2: (token: string, domCraftToken: string) => {
        const user = createFreshUserV2Apps(token);
        if (!user) {
            console.error('Failed to create fresh user for activateUserV2');
            return false;
        }
        const userId = user.id;


        const payload = processPayload({
            permissions: ['permissionA'],
            parentEmail: 'load-test-activate-{random}@epicgames.com'
        });

        const headers = addAuthHeader(domCraftToken);
        const response = http.post(`${BASE_URL}/v2/apps/${DOM_CRAFT_APP_ID}/users/${userId}/activate`, JSON.stringify(payload), { headers });
        return check(response, {
            'activate-user-v2 status is 200 or 201': (r) => r.status === 200 || r.status === 201,
            'activate-user-v2 has fresh user': () => userId !== undefined,
            "activate-user-v2 response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    requestPermissions: (token: string) => {
        const user = createFreshUserV2Apps(token);
        if (!user) {
            console.error('Failed to create fresh user for requestPermissions');
            return false;
        }
        const payload = processPayload({
            parentEmail: 'load-test-permissions-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        });

        const headers = addAuthHeader(token);
        const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${user.id}/request-permissions`, JSON.stringify(payload), { headers });
        return check(response, {
            'request-permissions status is 200': (r) => r.status === 200,
            "request-permissions response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    getPermissions: (token: string) => {
        const userId = getRandomUserId();
        const headers = addAuthHeader(token);
        const response = http.get(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${userId}/permissions`, { headers });
        return check(response, {
            'get-permissions status is 200': (r) => r.status === 200,
            "get-permissions response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    getPermissionsExtended: (token: string) => {
        const userId = getRandomUserId();
        const headers = addAuthHeader(token);
        const response = http.get(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${userId}/permissions?extended=true`, { headers });
        return check(response, {
            'get-permissions-extended status is 200': (r) => r.status === 200,
            "get-permissions-extended response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    reviewPermissions: (token: string) => {
        const userId = getRandomUserId();
        const headers = addAuthHeader(token);
        const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${userId}/review-permissions`, JSON.stringify({}), { headers });
        return check(response, {
            'review-permissions status is 201': (r) => r.status === 201,
            "review-permissions response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    updateParentEmail: (token: string) => {
        const userId = getRandomUserId();
        const payload = processPayload({
            parentEmail: 'load-test-update-{random}@epicgames.com',
        });

        const headers = addAuthHeader(token);
        const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${userId}/update-parent-email`, JSON.stringify(payload), { headers });
        return check(response, {
            'update-parent-email status is 204': (r) => r.status === 204,
            "update-parent-email response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    deleteUserActivation: (token: string) => {
        const user = createFreshUserV2Apps(token);
        if (!user) {
            console.error('Failed to create fresh user for deleteUser');
            return false;
        }
        const headers = addAuthHeader(token);
        const response = http.del(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${user.id}`, null, { headers });
        return check(response, {
            'delete-user status is 204': (r) => r.status === 204,
            "delete-user response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },
};
