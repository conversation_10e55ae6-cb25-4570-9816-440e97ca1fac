import { BadRequestException } from '@nestjs/common';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';
import { v4 as uuidv4 } from 'uuid';

import { InternalAdminService } from './internal-admin.service';
import { AppService } from '../app/app.service';
import { IAppInfo } from '../app/types';
import { SettingsService } from '../common/services/settings/settings.service';
import { Testing } from '../common/utils';
import { UserService } from '../user/user.service';

describe('InternalAdminService', () => {
  let service: InternalAdminService;
  let appService: AppService;
  let settingsService: SettingsService;
  let userService: UserService;

  beforeEach(async () => {
    const module = await Testing.createModule({
      providers: [InternalAdminService],
    });

    service = module.get(InternalAdminService);
    appService = module.get(AppService);
    settingsService = module.get(SettingsService);
    userService = module.get(UserService);
  });

  describe('getUserSettingsToUpdateUserDOB', () => {
    const orgEnvId = uuidv4();

    it('should return settings after a dob update', async () => {
      const mockSetting = { definition: { namespace: 'setting' } } as UserSettingValueDTO;

      jest
        .spyOn(appService, 'getAppInfoByUser')
        .mockResolvedValueOnce({ productId: 'prodId', credentials: { clientId: '', secret: '' } });
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([mockSetting]);

      await expect(
        service.getUserSettingsToUpdateUserDOB(orgEnvId, { userId: 123, dateOfBirth: '2020-01-01', location: 'US' }),
      ).resolves.toEqual([mockSetting]);
    });

    it('should throw if dob is missing', async () => {
      const mockError = new BadRequestException('dateOfBirth is missing');
      await expect(
        service.getUserSettingsToUpdateUserDOB(orgEnvId, { userId: 123, dateOfBirth: '', location: 'US' }),
      ).rejects.toThrow(mockError);
    });
  });

  describe('deleteUserAccount', () => {
    const orgEnvId = 'test-org-env-id';
    const userId = 123;
    const mockCredentials = { clientId: 'client-id', secret: 'top-secret' };

    beforeEach(() => {
      jest.spyOn(appService, 'getAppInfoByUser').mockResolvedValue({
        productId: 456,
        credentials: mockCredentials,
      } as unknown as IAppInfo);
      jest.spyOn(userService, 'deleteUser').mockResolvedValue();
    });

    it('should get app info for the user and delete the user', async () => {
      const getAppInfoSpy = jest.spyOn(appService, 'getAppInfoByUser');
      const deleteUserSpy = jest.spyOn(userService, 'deleteUser');

      await service.deleteUserAccount(orgEnvId, userId);

      expect(getAppInfoSpy).toHaveBeenCalledWith(orgEnvId, userId);
      expect(deleteUserSpy).toHaveBeenCalledWith(orgEnvId, userId, mockCredentials);
    });

    it('should throw an error if getAppInfoByUser fails', async () => {
      const error = new Error('App info not found');
      jest.spyOn(appService, 'getAppInfoByUser').mockRejectedValue(error);

      await expect(service.deleteUserAccount(orgEnvId, userId)).rejects.toThrow(error);
    });
  });
});
