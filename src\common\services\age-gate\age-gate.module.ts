import { Module } from '@nestjs/common';
import {
  AGE_GATE_API_CLIENT_INJECT_KEY,
  AGE_GATE_API_CLIENT_UPSTREAM,
  ageGateApiPlugin,
} from '@superawesome/freekws-agegate-api-common';
import { NestJsClient, TNestJsHttpClientConfig } from '@superawesome/freekws-clients-nestjs';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import type { AxiosError } from 'axios';
import CircuitBreaker from 'opossum';

import { AgeGateService } from './age-gate.service';
import { CommonModule } from '../../common.module';
import { ConfigService } from '../config/config.service';

@Module({
  imports: [CommonModule],
  providers: [
    AgeGateService,
    {
      provide: AGE_GATE_API_CLIENT_INJECT_KEY,
      useFactory: (configService: ConfigService, metricsService: MetricsService) => {
        const circuitBreakerConfig = configService.getAgeGateCircuitBreakerConfig();
        const { baseURL, ...ageGateServiceClientConfig } = configService.getAgeGateService();
        const clientConfig: TNestJsHttpClientConfig = {
          timeout: ageGateServiceClientConfig.timeoutMs,
          upstream: AGE_GATE_API_CLIENT_UPSTREAM,
          retry: {
            retries: ageGateServiceClientConfig.retries,
            initalRetryDelay: ageGateServiceClientConfig.initialRetryDelay,
            bailOnStatus: ageGateServiceClientConfig.bailOnStatus,
          },
          circuitBreaker: {
            timeout: circuitBreakerConfig.timeoutMs,
            errorThresholdPercentage: circuitBreakerConfig.errorThresholdPercentage,
            resetTimeout: circuitBreakerConfig.resetTimeoutMs,
            errorFilter: (error: AxiosError) => {
              return error.response?.status && error.response.status >= 400 && error.response.status < 500;
            },
          } as CircuitBreaker.Options,
        };
        return new NestJsClient(ageGateApiPlugin, baseURL, metricsService.metrics, clientConfig);
      },
      inject: [ConfigService, MetricsService],
    },
  ],
  exports: [AgeGateService],
})
export class AgeGateModule {}
