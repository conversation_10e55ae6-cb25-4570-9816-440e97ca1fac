import {
  BadRequestException,
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UpdateUserDateOfBirthDTO } from '@superawesome/freekws-classic-wrapper-common';
import { CheckPolicies, KeycloakAuthGuard, PoliciesGuard } from '@superawesome/freekws-nestjs-guards';
import { And, HasAzp } from '@superawesome/freekws-nestjs-guards/policies';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';

import { InternalAdminService } from './internal-admin.service';
import { EAPITags } from '../common/types';
import { UpdateUserDOBSchema } from '../org-env/types';
import { UserDeletionDTO } from '../user/user.dto';

@Controller('/internal-admin')
@UseGuards(KeycloakAuthGuard)
export class InternalAdminController {
  constructor(private readonly internalAdminService: InternalAdminService) {}

  @ApiOperation({
    summary: "Internal Admin Endpoint: Update the user's dob",
    description: `Intended only for classic customers. Update the user's DOB by requesting settings with a provided dob and location.`,
  })
  @ApiTags(EAPITags.InternalAdmin)
  @ApiOkResponse({ schema: UpdateUserDOBSchema })
  @Put('/org-envs/:orgEnvId/users/:userId/dob')
  @HttpCode(HttpStatus.OK)
  @UseGuards(PoliciesGuard)
  @CheckPolicies({
    useClass: And,
    inject: [new HasAzp('devportal-api')],
  })
  async updateUserDOB(
    @Param('orgEnvId') orgEnvId: string,
    @Param('userId', new ParseIntPipe()) userId: number,
    @Body() { dateOfBirth, location }: UpdateUserDateOfBirthDTO,
  ): Promise<UserSettingValueDTO[]> {
    // updating location won't work until tis ticket is resolved: https://superawesomeltd.atlassian.net/browse/K3S-2344
    return this.internalAdminService.getUserSettingsToUpdateUserDOB(orgEnvId, { userId, dateOfBirth, location });
  }

  @ApiOperation({
    summary: "Internal Admin Endpoint: Delete a user's account",
    description: `Intended only for classic customers. Delete a user's account.`,
  })
  @ApiTags(EAPITags.InternalAdmin)
  @ApiOkResponse()
  @Post('/org-envs/:orgEnvId/users/:userId/delete-account')
  @HttpCode(HttpStatus.OK)
  @UseGuards(PoliciesGuard)
  @CheckPolicies({
    useClass: And,
    inject: [new HasAzp('devportal-api')],
  })
  async deleteUserAccount(
    @Param('orgEnvId') orgEnvId: string,
    @Param('userId', new ParseIntPipe()) userId: number,
    @Body() body: UserDeletionDTO,
  ) {
    if (body.password) {
      throw new BadRequestException('Password is deprecated and should not be used');
    }

    await this.internalAdminService.deleteUserAccount(orgEnvId, userId);
  }
}
