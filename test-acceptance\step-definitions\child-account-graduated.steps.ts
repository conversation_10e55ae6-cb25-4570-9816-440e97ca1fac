import { DataTable, setDefaultTimeout } from '@cucumber/cucumber';
import { HttpRequestConfig } from '@superawesome/freekws-common-http-client';
import { SALogger } from '@superawesome/freekws-common-logger';
import { SettingsUserGraduatedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/settings-user-graduated.payload.dto';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';
import assert from 'assert';
import { binding, given, then, when } from 'cucumber-tsflow';

import { OrgLevelWebhookPayload } from '../../src/webhook/types';
import { Utils } from '../../test/utils';
import { orgEnvHost } from '../fixtures/org-env.fixture';
import { UATUtils } from '../utils';

setDefaultTimeout(60 * 1000);
@binding([SALogger])
export class ChildAccountGraduatedSteps {
  userId: string;
  responseStatus: number;
  orgId: string;
  secret: string;

  @given('organization with secret')
  org_with_credentials(table: DataTable) {
    const data = table.rowsHash() as {
      orgId: string;
      secret: string;
    };

    this.orgId = data.orgId;
    this.secret = data.secret;
  }

  @when('child-account-graduated for {string} is triggered')
  async child_Account_Graduated(userId: string): Promise<void> {
    const timestamp = Date.now();
    const body: OrgLevelWebhookPayload<SettingsUserGraduatedPayloadDTO> = {
      name: EWebhookName.SETTINGS_USER_GRADUATED,
      time: timestamp,
      orgId: this.orgId,
      payload: {
        userId: userId,
      },
    };
    const signature = Utils.generateKwsSignature(timestamp, body, this.secret);

    const request = {
      url: `/v1/webhooks/child-account-graduated`,
      method: 'POST',
      data: body,
      headers: {
        'x-kws-signature': `t=${timestamp},v1=${signature}`,
        'x-forwarded-host': orgEnvHost,
      },
    } as HttpRequestConfig;

    const response = await UATUtils.buildClassicWrapperClient().request(request);
    this.responseStatus = response.status;
  }

  @then('http status code no-content is returned')
  http_status_code_ok_is_returned() {
    assert.equal(this.responseStatus, 204);
  }
}
