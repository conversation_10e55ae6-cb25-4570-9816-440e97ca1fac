{"name": "k6", "version": "1.0.0", "main": "index.js", "scripts": {"build": "esbuild load-testing.ts --bundle --platform=browser --format=esm --outfile=dist/load-testing.js --target=es2017 --external:k6 --external:k6/http --external:k6/metrics --external:k6/options", "build:configurable": "esbuild configurable-test.ts --bundle --platform=browser --format=esm --outfile=dist/configurable-test.js --target=es2017 --external:k6 --external:k6/http --external:k6/metrics --external:k6/options", "build:webhook": "esbuild webhook-load-tests.ts --bundle --platform=browser --format=esm --outfile=dist/webhook-load-tests.js --target=es2017 --external:k6 --external:k6/http --external:k6/metrics --external:k6/options", "build:sequential": "esbuild sequential-load-test.ts --bundle --platform=browser --format=esm --outfile=dist/sequential-load-test.js --target=es2017 --external:k6 --external:k6/http --external:k6/metrics --external:k6/options", "build:sequential-scenarios": "esbuild sequential-scenarios-test.ts --bundle --platform=browser --format=esm --outfile=dist/sequential-scenarios-test.js --target=es2017 --external:k6 --external:k6/http --external:k6/metrics --external:k6/options", "load-test": "npm run build && dotenv -- k6 run load-testing.ts --env BASE_URL=http://localhost:7001", "test:configurable": "npm run build:configurable && dotenv -- k6 run dist/configurable-test.js --env BASE_URL=http://localhost:7001", "test:users": "npm run build:configurable && dotenv -- k6 run dist/configurable-test.js --env BASE_URL=http://localhost:7001 --env WORKFLOWS=userData", "test:permissions": "npm run build:configurable && dotenv -- k6 run dist/configurable-test.js --env BASE_URL=http://localhost:7001 --env WORKFLOWS=permissions", "test:child-age": "npm run build:configurable && dotenv -- k6 run dist/configurable-test.js --env BASE_URL=http://localhost:7001 --env WORKFLOWS=childAge", "test:all": "npm run build:configurable && dotenv -- k6 run dist/configurable-test.js --env BASE_URL=http://localhost:7001 --env WORKFLOWS=basic,userData,childAge,permissions", "test:webhooks": "npm run build:webhook && dotenv -- k6 run dist/webhook-load-tests.js --env BASE_URL=http://localhost:7001", "test:sequential": "npm run build:sequential && dotenv -- k6 run dist/sequential-load-test.js --env TARGET_QPS=100", "test:sequential-stress": "npm run build:sequential && dotenv -- k6 run dist/sequential-load-test.js --env TARGET_QPS=300", "test:sequential-scenarios": "npm run build:sequential-scenarios && dotenv -- k6 run dist/sequential-scenarios-test.js", "test:sequential-scenarios-stress": "npm run build:sequential-scenarios && dotenv -- k6 run dist/sequential-scenarios-test.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"k6": "^0.0.0"}, "devDependencies": {"@types/k6": "^1.0.1", "dotenv-cli": "^8.0.0", "esbuild": "^0.25.1", "typescript": "^5.8.2"}}