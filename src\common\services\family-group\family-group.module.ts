import { Module } from '@nestjs/common';
import { NestJsClient, TNestJsHttpClientConfig } from '@superawesome/freekws-clients-nestjs';
import {
  FAMILY_SERVICE_CLIENT_INJECT_KEY,
  FAMILY_SERVICE_CLIENT_UPSTREAM,
  familyServicePlugin,
} from '@superawesome/freekws-family-service-common';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import { AxiosError } from 'axios';
import CircuitBreaker from 'opossum';

import { FamilyGroupService } from './family-group.service';
import { CommonModule } from '../../common.module';
import { ConfigService } from '../config/config.service';
import { KeycloakModule } from '../keycloak/keycloak.module';

@Module({
  imports: [CommonModule, KeycloakModule],
  providers: [
    FamilyGroupService,
    {
      provide: FAMILY_SERVICE_CLIENT_INJECT_KEY,
      useFactory: (config: ConfigService, { metrics }: MetricsService) => {
        const { baseURL, ...clientOptions } = config.getFamilyService();
        const circuitBreakerConfig = config.getFamilyServiceCircuitBreakerConfig();
        const clientConfig: TNestJsHttpClientConfig = {
          timeout: clientOptions.timeoutMs,
          upstream: FAMILY_SERVICE_CLIENT_UPSTREAM,
          retry: {
            retries: clientOptions.retries,
            initalRetryDelay: clientOptions.initialRetryDelay,
            bailOnStatus: clientOptions.bailOnStatus,
          },
          circuitBreaker: {
            timeout: circuitBreakerConfig.timeoutMs,
            errorThresholdPercentage: circuitBreakerConfig.errorThresholdPercentage,
            resetTimeout: circuitBreakerConfig.resetTimeoutMs,
            errorFilter: (error: AxiosError) => {
              return error.response?.status && error.response.status >= 400 && error.response.status < 500;
            },
          } as CircuitBreaker.Options,
        };
        return new NestJsClient(familyServicePlugin, baseURL, metrics, clientConfig);
      },
      inject: [ConfigService, MetricsService],
    },
  ],
  exports: [FamilyGroupService],
})
export class FamilyGroupModule {}
