import { BadRequestException, CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Span } from 'nestjs-ddtrace';

import { CLIENT_OAUTH_TOKEN } from './inject-client-oauth.decorator';
import { OauthFastifyRequest } from './types';
import { AppService } from '../../../app/app.service';
import { IAppOauthClientCredentials } from '../../../app/types';
import { OrgEnvService } from '../../../org-env/org-env.service';

@Injectable()
@Span()
export class InjectClientOauthGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly appService: AppService,
    private readonly orgEnvService: OrgEnvService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    if (!this.reflector.get(CLIENT_OAUTH_TOKEN, context.getHandler())) {
      return true;
    }

    const request = context.switchToHttp().getRequest<OauthFastifyRequest>();
    const credentials = this.getCredentialsFrom(request);

    const orgEnvId = await this.orgEnvService.getOrgEnvFromRequest(request);

    try {
      request.raw.oauthClient = await this.appService.getAppOauthClient(
        credentials as IAppOauthClientCredentials,
        orgEnvId.id,
      );
    } catch (error) {
      throw new BadRequestException('Client credentials are invalid', { cause: error });
    }

    return true;
  }

  private getCredentialsFrom(request: OauthFastifyRequest) {
    const requestHeaders = request.headers;

    let authorizationHeader: string | undefined;
    if (Array.isArray(requestHeaders['authorization'])) {
      authorizationHeader = requestHeaders['authorization'][0];
    } else {
      authorizationHeader = requestHeaders['authorization'];
    }

    let clientId: string | undefined;
    let clientSecret: string | undefined;
    if (authorizationHeader?.startsWith('Basic ')) {
      const [id, secret] = Buffer.from(authorizationHeader.slice(6), 'base64').toString('utf8').split(':');
      clientId = id;
      clientSecret = secret;
    } else {
      clientId = request.body.client_id;
      clientSecret = request.body.client_secret;
    }

    if (!clientId) {
      throw new BadRequestException('Invalid or missing client_id parameter');
    }

    if (!clientSecret) {
      throw new BadRequestException('Missing client_secret parameter');
    }

    const credentials: IAppOauthClientCredentials = {
      clientId,
      secret: clientSecret,
    };

    return credentials;
  }
}
