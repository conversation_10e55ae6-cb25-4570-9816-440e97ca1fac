import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { JWK } from './jwk.entity';

@Injectable()
export class JwkRepository {
  constructor(
    @InjectRepository(JWK)
    private readonly jwkRepository: Repository<JWK>,
  ) {}

  async getRecentJwks(limit = 20): Promise<JWK[]> {
    return this.jwkRepository.find({
      order: {
        createdAt: 'DESC',
      },
      take: limit,
    });
  }
}
