import { sleep } from 'k6';
import { Options } from 'k6/options';
import { requests, getOAuthToken } from './requests';

// Configuration
const TARGET_QPS = parseInt(__ENV.TARGET_QPS ?? '100');
const TEST_DURATION = parseInt(__ENV.TEST_DURATION ?? '60'); // seconds per endpoint
const COOLDOWN_DURATION = parseInt(__ENV.COOLDOWN_DURATION ?? '60'); // seconds between endpoints
const CLIENT_ID = __ENV.CLIENT_ID || 'your-client-id';
const CLIENT_SECRET = __ENV.CLIENT_SECRET || 'your-client-secret';
const DOM_CRAFT_CLIENT_ID = __ENV.DOM_CRAFT_CLIENT_ID || 'dom-craft-client-id';
const DOM_CRAFT_CLIENT_SECRET = __ENV.DOM_CRAFT_SECRET || 'dom-craft-client-secret';
export const TARGET_TIME_CREATION_REQUEST = TARGET_QPS <= 100 ? 1000 : 1500;
export const TARGET_TIME_OTHER_REQUEST = TARGET_QPS <= 100 ? 200 : 500;

const TARGET_VUS = TARGET_QPS;

const endpointSequence = [
    { name: 'healthcheck', func: requests.healthcheck, needsAuth: false },
    { name: 'getJwks', func: requests.getJwks, needsAuth: false },
    { name: 'childAge', func: requests.childAge, needsAuth: false },
    { name: 'checkUsername', func: requests.checkUsername, needsAuth: false },
    { name: 'createUser', func: requests.createUser, needsAuth: true },
    { name: 'createUserV2', func: requests.createUserV2, needsAuth: true },
    { name: 'getUser', func: requests.getUser, needsAuth: true },
    { name: 'activateUser', func: requests.activateUser, needsAuth: true },
    { name: 'activateUserV2', func: requests.activateUserV2, needsAuth: true },
    { name: 'requestPermissions', func: requests.requestPermissions, needsAuth: true },
    { name: 'getPermissions', func: requests.getPermissions, needsAuth: true },
    { name: 'getPermissionsExtended', func: requests.getPermissionsExtended, needsAuth: true },
    { name: 'reviewPermissions', func: requests.reviewPermissions, needsAuth: true },
    { name: 'updateParentEmail', func: requests.updateParentEmail, needsAuth: true },
    { name: 'deleteUserActivation', func: requests.deleteUserActivation, needsAuth: true }, // Test when emails are disabled
];

// Build scenarios for sequential execution
function buildScenarios() {
    const scenarios: any = {};
    let currentStartTime = 0;

    const rampUpTime = Math.ceil(TEST_DURATION * 0.2);
    const steadyTime = Math.ceil(TEST_DURATION * 0.6);
    const rampDownTime = Math.ceil(TEST_DURATION * 0.2);

    endpointSequence.forEach((endpoint, index) => {
        scenarios[endpoint.name] = {
            executor: 'ramping-arrival-rate',
            exec: endpoint.name,
            startTime: `${currentStartTime}s`,
            preAllocatedVUs: 500,
            maxVUs: 1000,
            stages: [
                { duration: `${rampUpTime}s`, target: TARGET_VUS },
                { duration: `${steadyTime}s`, target: TARGET_VUS },
                { duration: `${rampDownTime}s`, target: 0 },
            ],
            gracefulStop: '60s',
        };

        // Next scenario starts after current test duration + cooldown
        currentStartTime += TEST_DURATION + COOLDOWN_DURATION;
    });

    return scenarios;
}

export const options: Options = {
    scenarios: buildScenarios(),
    thresholds: {
        http_req_failed: ['rate<0.05'], // Less than 5% errors
        http_req_duration: ['p(95)<2000'], // 95% under 2 seconds
    },
};

export function setup() {
    const token = getOAuthToken(CLIENT_ID, CLIENT_SECRET);

    if (!token) {
        throw new Error('Failed to get OAuth token javi-test');
    }

    const domCraftToken = getOAuthToken(DOM_CRAFT_CLIENT_ID, DOM_CRAFT_CLIENT_SECRET);
    if (!domCraftToken) {
        throw new Error('Failed to get OAuth token for dom craft');
    }

    console.log(`Sequential scenarios test starting:`);
    console.log(`- Target QPS: ${TARGET_QPS}`);
    console.log(`- Target VUs: ${TARGET_VUS}`);
    console.log(`- Test duration per endpoint: ${TEST_DURATION}s`);
    console.log(`- Cooldown between endpoints: ${COOLDOWN_DURATION}s`);
    console.log(`- Total endpoints: ${endpointSequence.length}`);

    const totalTime = (endpointSequence.length * (TEST_DURATION + COOLDOWN_DURATION)) / 60;
    console.log(`- Estimated total time: ${totalTime.toFixed(1)} minutes`);

    return { token, domCraftToken };
}

type SettingsReturn = ReturnType<typeof setup>;

// Export individual endpoint functions for K6 scenarios
export function healthcheck({ token }: SettingsReturn) {
    requests.healthcheck();
    sleep(0.1);
}

export function getJwks({ token }: SettingsReturn) {
    requests.getJwks();
    sleep(0.1);
}

export function childAge({ token }: SettingsReturn) {
    requests.childAge();
    sleep(0.1);
}

export function checkUsername({ token }: SettingsReturn) {
    requests.checkUsername();
    sleep(0.1);
}

export function createUser({ token }: SettingsReturn) {
    requests.createUser(token);
    sleep(0.1);
}

export function createUserV2({ token }: SettingsReturn) {
    requests.createUserV2(token);
    sleep(0.1);
}

export function getUser({ token }: SettingsReturn) {
    requests.getUser(token);
    sleep(0.1);
}

export function activateUser({ token }: SettingsReturn) {
    requests.activateUser(token);
    sleep(0.1);
}

export function activateUserV2({ token, domCraftToken }: SettingsReturn) {
    requests.activateUserV2(token, domCraftToken);
    sleep(0.1);
}

export function requestPermissions({ token }: SettingsReturn) {
    requests.requestPermissions(token);
    sleep(0.1);
}

export function getPermissions({ token }: SettingsReturn) {
    requests.getPermissions(token);
    sleep(0.1);
}

export function getPermissionsExtended({ token }: SettingsReturn) {
    requests.getPermissionsExtended(token);
    sleep(0.1);
}

export function reviewPermissions({ token }: SettingsReturn) {
    requests.reviewPermissions(token);
    sleep(0.1);
}

export function updateParentEmail({ token }: SettingsReturn) {
    requests.updateParentEmail(token);
    sleep(0.1);
}

export function deleteUserActivation({ token }: SettingsReturn) {
    requests.deleteUserActivation(token);
    sleep(0.1);
}
