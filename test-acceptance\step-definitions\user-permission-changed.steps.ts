import { DataTable, setDefaultTimeout } from '@cucumber/cucumber';
import { HttpRequestConfig } from '@superawesome/freekws-common-http-client';
import { SALogger } from '@superawesome/freekws-common-logger';
import { SettingsEffectiveValuesChangedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';
import assert from 'assert';
import { binding, given, then, when } from 'cucumber-tsflow';

import { AppLevelWebhookPayload } from '../../src/webhook/types';
import { Utils } from '../../test/utils';
import { orgEnvHost } from '../fixtures/org-env.fixture';
import { UATUtils } from '../utils';

setDefaultTimeout(60 * 1000);
@binding([SALogger])
export class UserPermissionChangedSteps {
  userId: string;
  responseStatus: number;
  appId: string;
  secret: string;

  @given('app with secret')
  app_with_secret(table: DataTable) {
    const data = table.rowsHash() as {
      appId: string;
      secret: string;
    };

    this.appId = data.appId;
    this.secret = data.secret;
  }

  @when('user-permission-changed for {string} is triggered')
  async user_permission_changed(userId: string): Promise<void> {
    const timestamp = Date.now();
    const body: AppLevelWebhookPayload<SettingsEffectiveValuesChangedPayloadDTO> = {
      name: EWebhookName.SETTINGS_EFFECTIVE_VALUES_CHANGED,
      time: timestamp,
      orgId: '123',
      productId: '7794e949-9fed-43eb-80be-4b504dc43b1b',
      payload: {
        userId: userId,
        settings: [
          {
            namespace: 'namespace',
            settingName: 'dataShare',
            effectiveValue: true,
            effectiveSource: 'source',
            trigger: 'trigger',
          },
        ],
      },
    };
    const signature = Utils.generateKwsSignature(timestamp, body, this.secret);

    const request = {
      url: `/v1/webhooks/settings-effective-values-changed`,
      method: 'POST',
      data: body,
      headers: {
        'x-kws-signature': `t=${timestamp},v1=${signature}`,
        'x-forwarded-host': orgEnvHost,
      },
    } as HttpRequestConfig;

    const response = await UATUtils.buildClassicWrapperClient().request(request);
    this.responseStatus = response.status;
  }

  @then('user-permission-changed returns http status code no-content')
  http_status_code_ok_is_returned() {
    assert.equal(this.responseStatus, 204);
  }
}
