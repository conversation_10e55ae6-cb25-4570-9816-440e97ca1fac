import { Module } from '@nestjs/common';
import { NestJsClient, TNestJsHttpClientConfig } from '@superawesome/freekws-clients-nestjs';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import {
  PREVERIFICATION_SERVICE_CLIENT_INJECT_KEY,
  PREVERIFICATION_SERVICE_CLIENT_UPSTREAM,
  preverificationBackendPlugin,
} from '@superawesome/freekws-preverification-service-common';
import { AxiosError } from 'axios';
import CircuitBreaker from 'opossum';

import { PreVerificationService } from './pre-verification.service';
import { CommonModule } from '../../common.module';
import { ConfigService } from '../config/config.service';

@Module({
  imports: [CommonModule],
  providers: [
    {
      provide: PREVERIFICATION_SERVICE_CLIENT_INJECT_KEY,
      useFactory: (config: ConfigService, { metrics }: MetricsService) => {
        const { baseURL, ...clientOptions } = config.getPreVerificationService();
        const circuitBreakerConfig = config.getPreVerificationServiceCircuitBreakerConfig();
        const clientConfig: TNestJsHttpClientConfig = {
          timeout: clientOptions.timeoutMs,
          upstream: PREVERIFICATION_SERVICE_CLIENT_UPSTREAM,
          retry: {
            retries: clientOptions.retries,
            initalRetryDelay: clientOptions.initialRetryDelay,
            bailOnStatus: clientOptions.bailOnStatus,
          },
          circuitBreaker: {
            timeout: circuitBreakerConfig.timeoutMs,
            errorThresholdPercentage: circuitBreakerConfig.errorThresholdPercentage,
            resetTimeout: circuitBreakerConfig.resetTimeoutMs,
            errorFilter: (error: AxiosError) => {
              return error.response?.status && error.response.status >= 400 && error.response.status < 500;
            },
          } as CircuitBreaker.Options,
        };
        return new NestJsClient(preverificationBackendPlugin, baseURL, metrics, clientConfig);
      },
      inject: [ConfigService, MetricsService],
    },
    PreVerificationService,
  ],
  exports: [PreVerificationService],
})
export class PreVerificationModule {}
