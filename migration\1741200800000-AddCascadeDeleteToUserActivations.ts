import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCascadeDeleteToUserActivations1741200800000 implements MigrationInterface {
    name = 'AddCascadeDeleteToUserActivations1741200800000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // First drop the existing foreign key constraint
        await queryRunner.query(`
            ALTER TABLE "activation"
                DROP CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9"
        `);

        // Re-add the constraint with CASCADE delete
        await queryRunner.query(`
            ALTER TABLE "activation"
                ADD CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9"
                    FOREIGN KEY ("userId", "orgEnvId")
                        REFERENCES "user" ("id", "orgEnvId")
                        ON DELETE CASCADE
                        ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert back to the original constraint without CASCADE
        await queryRunner.query(`
            ALTER TABLE "activation"
                DROP CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9"
        `);

        await queryRunner.query(`
            ALTER TABLE "activation"
                ADD CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9"
                    FOREIGN KEY ("userId", "orgEnvId")
                        REFERENCES "user" ("id", "orgEnvId")
                        ON DELETE NO ACTION
                        ON UPDATE NO ACTION
        `);
    }
}