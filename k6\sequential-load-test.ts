import { sleep } from 'k6';
import { Options } from 'k6/options';
import { requests, getOAuthToken } from './requests';

// Configuration
const TARGET_QPS = parseInt(__ENV.TARGET_QPS || '100');
const TEST_DURATION = parseInt(__ENV.TEST_DURATION || '60'); // seconds per endpoint
const COOLDOWN_DURATION = parseInt(__ENV.COOLDOWN_DURATION || '60'); // seconds between endpoints
const CLIENT_ID = __ENV.CLIENT_ID || 'your-client-id';
const CLIENT_SECRET = __ENV.CLIENT_SECRET || 'your-client-secret';

// Define endpoints to test in sequence
const endpointSequence = [
    { name: 'healthcheck', func: requests.healthcheck, needsAuth: false },
    { name: 'getJwks', func: requests.getJwks, needsAuth: false },
    { name: 'childAge', func: requests.childAge, needsAuth: false },
    { name: 'checkUsername', func: requests.checkUsername, needsAuth: false },
    { name: 'createUser', func: requests.createUser, needsAuth: true },
    { name: 'createUserV2', func: requests.createUserV2, needsAuth: true },
    { name: 'getUser', func: requests.getUser, needsAuth: true },
    { name: 'activateUser', func: requests.activateUser, needsAuth: true },
    { name: 'requestPermissions', func: requests.requestPermissions, needsAuth: true },
    { name: 'getPermissions', func: requests.getPermissions, needsAuth: true },
    { name: 'getPermissionsExtended', func: requests.getPermissionsExtended, needsAuth: true },
    { name: 'reviewPermissions', func: requests.reviewPermissions, needsAuth: true },
    { name: 'updateParentEmail', func: requests.updateParentEmail, needsAuth: true },
    // { name: 'deleteUser', func: requests.deleteUser, needsAuth: true },
];

// Calculate VUs needed for target QPS (rough estimate)
const TARGET_VUS = Math.max(1, Math.ceil(TARGET_QPS / 10));

// Build stages for all endpoints with ramp up/down and cooldowns
function buildStages() {
    const stages = [];
    const rampUpTime = Math.ceil(TEST_DURATION * 0.2);
    const steadyTime = Math.ceil(TEST_DURATION * 0.6);
    const rampDownTime = Math.ceil(TEST_DURATION * 0.2);

    for (let i = 0; i < endpointSequence.length; i++) {
        // Ramp up
        stages.push({ duration: `${rampUpTime}s`, target: TARGET_VUS });
        // Steady state
        stages.push({ duration: `${steadyTime}s`, target: TARGET_VUS });
        // Ramp down
        stages.push({ duration: `${rampDownTime}s`, target: 0 });
        // Cooldown (except for last endpoint)
        if (i < endpointSequence.length - 1) {
            stages.push({ duration: `${COOLDOWN_DURATION}s`, target: 0 });
        }
    }

    return stages;
}

export const options: Options = {
    stages: buildStages(),
    thresholds: {
        http_req_failed: ['rate<0.05'], // Less than 5% errors
        http_req_duration: ['p(95)<2000'], // 95% under 2 seconds
    },
};

// Global state to track current endpoint
let testStartTime = 0;
let currentEndpointIndex = 0;

export function setup() {
    const token = getOAuthToken(CLIENT_ID, CLIENT_SECRET);

    if (!token) {
        throw new Error('Failed to get OAuth token');
    }

    console.log(`Sequential load test starting:`);
    console.log(`- Target QPS: ${TARGET_QPS}`);
    console.log(`- Target VUs: ${TARGET_VUS}`);
    console.log(`- Test duration per endpoint: ${TEST_DURATION}s`);
    console.log(`- Cooldown between endpoints: ${COOLDOWN_DURATION}s`);
    console.log(`- Total endpoints: ${endpointSequence.length}`);

    const totalTime = (endpointSequence.length * (TEST_DURATION + COOLDOWN_DURATION)) / 60;
    console.log(`- Estimated total time: ${totalTime.toFixed(1)} minutes`);

    return { token };
}

function getCurrentEndpointIndex(): number {
    const now = Date.now() / 1000;

    if (testStartTime === 0) {
        testStartTime = now;
    }

    const elapsedSeconds = now - testStartTime;
    const timePerEndpoint = TEST_DURATION + COOLDOWN_DURATION;

    return Math.floor(elapsedSeconds / timePerEndpoint);
}

export default function ({ token }: { token: string }) {
    const endpointIndex = getCurrentEndpointIndex();

    // Check if we've finished all endpoints
    if (endpointIndex >= endpointSequence.length) {
        return; // Test finished
    }

    // Update current endpoint index and log if changed
    if (endpointIndex !== currentEndpointIndex) {
        if (currentEndpointIndex < endpointSequence.length) {
            console.log(`Finished endpoint: ${endpointSequence[currentEndpointIndex].name}`);
        }
        currentEndpointIndex = endpointIndex;
        console.log(`Starting endpoint: ${endpointSequence[currentEndpointIndex].name}`);
    }

    // Execute the current endpoint
    const currentEndpoint = endpointSequence[currentEndpointIndex];

    try {
        currentEndpoint.func(token);
    } catch (error) {
        console.error(`Error in ${currentEndpoint.name}: ${error}`);
    }

    sleep(0.1);
}
