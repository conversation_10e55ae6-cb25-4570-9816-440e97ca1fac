import {
  AppUserParams,
  RequestUserPermissionsDTO,
  UpdateParentEmailDTO,
} from '@superawesome/freekws-classic-wrapper-common';

import { App } from './app.entity';
import { IClientCredentials } from '../org-env/types';

export type IAppOauthClient = {
  appId: number;
  orgEnvId: string;
  clientId: string;
  redirectUri?: string;
};

export type IAppOauthClientCredentials = {
  clientId: string;
  secret: string;
};

export type IAppInfo = {
  id: number;
  orgId: string;
  orgEnvId: string;
  productId: string;
  credentials: IClientCredentials;
  app: App;
};

export type TRequestUserPermissions = RequestUserPermissionsDTO & AppUserParams;

export type TUpdateParentEmailParams = AppUserParams & UpdateParentEmailDTO;
