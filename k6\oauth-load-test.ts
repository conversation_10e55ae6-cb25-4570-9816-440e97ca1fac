import { sleep } from 'k6';
import { Options } from 'k6/options';
import http from 'k6/http';
import { check } from 'k6';

// Configuration
const TARGET_QPS = parseInt(__ENV.TARGET_QPS ?? '100');
const TEST_DURATION = parseInt(__ENV.TEST_DURATION ?? '60');
const COOLDOWN_DURATION = parseInt(__ENV.COOLDOWN_DURATION ?? '60');
const CLIENT_ID = __ENV.CLIENT_ID || 'your-client-id';
const CLIENT_SECRET = __ENV.CLIENT_SECRET || 'your-client-secret';
const DOM_CRAFT_CLIENT_ID = __ENV.DOM_CRAFT_CLIENT_ID || 'dom-craft-client-id';
const DOM_CRAFT_CLIENT_SECRET = __ENV.DOM_CRAFT_SECRET || 'dom-craft-client-secret';

// Performance targets based on QPS (OAuth is authentication, so uses creation thresholds)
const TARGET_TIME_OAUTH_REQUEST = TARGET_QPS <= 100 ? 1000 : 1500;

const TARGET_VUS = TARGET_QPS;
const BASE_URL = __ENV.BASE_URL || 'http://localhost:7001';

const DEFAULT_HEADERS = {
    'x-forwarded-host': 'test.com',
    'Content-Type': 'application/x-www-form-urlencoded'
};

// OAuth endpoint request functions
const oauthRequests = {
    // Client credentials flow (app scope)
    oauthTokenClientCredentials: () => {
        const payload = {
            client_id: CLIENT_ID,
            client_secret: CLIENT_SECRET,
            grant_type: 'client_credentials',
            scope: 'app'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers: DEFAULT_HEADERS });
        
        return check(response, {
            'oauth-client-credentials status is 200': (r) => r.status === 200,
            'oauth-client-credentials has access_token': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.access_token !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-client-credentials has token_type': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.token_type === 'Bearer';
                } catch {
                    return false;
                }
            },
            'oauth-client-credentials response time': (r) => r.timings.duration < TARGET_TIME_OAUTH_REQUEST,
        });
    },

    // Client credentials flow for dom-craft app
    oauthTokenClientCredentialsDomCraft: () => {
        const payload = {
            client_id: DOM_CRAFT_CLIENT_ID,
            client_secret: DOM_CRAFT_CLIENT_SECRET,
            grant_type: 'client_credentials',
            scope: 'app'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers: DEFAULT_HEADERS });
        
        return check(response, {
            'oauth-domcraft-client-credentials status is 200': (r) => r.status === 200,
            'oauth-domcraft-client-credentials has access_token': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.access_token !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-domcraft-client-credentials response time': (r) => r.timings.duration < TARGET_TIME_OAUTH_REQUEST,
        });
    },

    // Password flow (user scope) - requires valid user credentials
    oauthTokenPassword: () => {
        // Use one of the test users - this would need valid credentials in real scenario
        const testUsers = [
            { username: 'testuser1', password: 'test-password' },
            { username: 'testuser2', password: 'test-password' },
            { username: 'testuser3', password: 'test-password' },
        ];
        
        const user = testUsers[Math.floor(Math.random() * testUsers.length)];
        
        const payload = {
            username: user.username,
            password: user.password,
            client_id: CLIENT_ID,
            client_secret: CLIENT_SECRET,
            grant_type: 'password',
            scope: 'user'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers: DEFAULT_HEADERS });
        
        return check(response, {
            'oauth-password status is 200 or 401': (r) => r.status === 200 || r.status === 401, // 401 expected for invalid users
            'oauth-password response time': (r) => r.timings.duration < TARGET_TIME_OAUTH_REQUEST,
            'oauth-password has proper error for invalid user': (r) => {
                if (r.status === 401) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.error !== undefined;
                    } catch {
                        return false;
                    }
                }
                return true; // If 200, that's also valid
            },
        });
    },

    // Invalid client credentials (error case)
    oauthTokenInvalidClient: () => {
        const payload = {
            client_id: 'invalid-client-id',
            client_secret: 'invalid-client-secret',
            grant_type: 'client_credentials',
            scope: 'app'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers: DEFAULT_HEADERS });
        
        return check(response, {
            'oauth-invalid-client status is 401': (r) => r.status === 401,
            'oauth-invalid-client has error': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.error !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-invalid-client response time': (r) => r.timings.duration < TARGET_TIME_OAUTH_REQUEST,
        });
    },

    // Invalid grant type (error case)
    oauthTokenInvalidGrantType: () => {
        const payload = {
            client_id: CLIENT_ID,
            client_secret: CLIENT_SECRET,
            grant_type: 'invalid_grant_type',
            scope: 'app'
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers: DEFAULT_HEADERS });
        
        return check(response, {
            'oauth-invalid-grant status is 400': (r) => r.status === 400,
            'oauth-invalid-grant has error': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.error === 'unsupported_grant_type';
                } catch {
                    return false;
                }
            },
            'oauth-invalid-grant response time': (r) => r.timings.duration < TARGET_TIME_OAUTH_REQUEST,
        });
    },

    // Missing required parameters (error case)
    oauthTokenMissingParams: () => {
        const payload = {
            client_id: CLIENT_ID,
            // Missing client_secret, grant_type, scope
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers: DEFAULT_HEADERS });
        
        return check(response, {
            'oauth-missing-params status is 400': (r) => r.status === 400,
            'oauth-missing-params has error': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.error !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-missing-params response time': (r) => r.timings.duration < TARGET_TIME_OAUTH_REQUEST,
        });
    },
};

// Define OAuth endpoint sequence
const oauthEndpointSequence = [
    { name: 'oauthTokenClientCredentials', func: oauthRequests.oauthTokenClientCredentials },
    { name: 'oauthTokenClientCredentialsDomCraft', func: oauthRequests.oauthTokenClientCredentialsDomCraft },
    { name: 'oauthTokenPassword', func: oauthRequests.oauthTokenPassword },
    { name: 'oauthTokenInvalidClient', func: oauthRequests.oauthTokenInvalidClient },
    { name: 'oauthTokenInvalidGrantType', func: oauthRequests.oauthTokenInvalidGrantType },
    { name: 'oauthTokenMissingParams', func: oauthRequests.oauthTokenMissingParams },
];

// Build scenarios for sequential OAuth testing
function buildOAuthScenarios() {
    const scenarios: any = {};
    let currentStartTime = 0;

    const rampUpTime = Math.ceil(TEST_DURATION * 0.2);
    const steadyTime = Math.ceil(TEST_DURATION * 0.6);
    const rampDownTime = Math.ceil(TEST_DURATION * 0.2);

    oauthEndpointSequence.forEach((endpoint, index) => {
        scenarios[endpoint.name] = {
            executor: 'ramping-arrival-rate',
            exec: endpoint.name,
            startTime: `${currentStartTime}s`,
            preAllocatedVUs: 500,
            maxVUs: 1000,
            stages: [
                { duration: `${rampUpTime}s`, target: TARGET_VUS },
                { duration: `${steadyTime}s`, target: TARGET_VUS },
                { duration: `${rampDownTime}s`, target: 0 },
            ],
            gracefulStop: '60s',
        };

        currentStartTime += TEST_DURATION + COOLDOWN_DURATION;
    });

    return scenarios;
}

export const options: Options = {
    scenarios: buildOAuthScenarios(),
    thresholds: {
        http_req_failed: ['rate<0.05'], // Less than 5% errors
        http_req_duration: ['p(95)<2000'], // 95% under 2 seconds
        // OAuth-specific thresholds
        'http_req_duration{scenario:oauthTokenClientCredentials}': [`p(99)<${TARGET_TIME_OAUTH_REQUEST}`],
        'http_req_duration{scenario:oauthTokenClientCredentialsDomCraft}': [`p(99)<${TARGET_TIME_OAUTH_REQUEST}`],
        'http_req_duration{scenario:oauthTokenPassword}': [`p(99)<${TARGET_TIME_OAUTH_REQUEST}`],
    },
};

export function setup() {
    console.log(`OAuth load test starting:`);
    console.log(`- Target QPS: ${TARGET_QPS}`);
    console.log(`- Target VUs: ${TARGET_VUS}`);
    console.log(`- Test duration per endpoint: ${TEST_DURATION}s`);
    console.log(`- Cooldown between endpoints: ${COOLDOWN_DURATION}s`);
    console.log(`- Total OAuth endpoints: ${oauthEndpointSequence.length}`);
    console.log(`- OAuth target response time: ${TARGET_TIME_OAUTH_REQUEST}ms`);

    const totalTime = (oauthEndpointSequence.length * (TEST_DURATION + COOLDOWN_DURATION)) / 60;
    console.log(`- Estimated total time: ${totalTime.toFixed(1)} minutes`);

    return {};
}

// Export individual OAuth endpoint functions for K6 scenarios
export function oauthTokenClientCredentials() {
    oauthRequests.oauthTokenClientCredentials();
    sleep(0.1);
}

export function oauthTokenClientCredentialsDomCraft() {
    oauthRequests.oauthTokenClientCredentialsDomCraft();
    sleep(0.1);
}

export function oauthTokenPassword() {
    oauthRequests.oauthTokenPassword();
    sleep(0.1);
}

export function oauthTokenInvalidClient() {
    oauthRequests.oauthTokenInvalidClient();
    sleep(0.1);
}

export function oauthTokenInvalidGrantType() {
    oauthRequests.oauthTokenInvalidGrantType();
    sleep(0.1);
}

export function oauthTokenMissingParams() {
    oauthRequests.oauthTokenMissingParams();
    sleep(0.1);
}
