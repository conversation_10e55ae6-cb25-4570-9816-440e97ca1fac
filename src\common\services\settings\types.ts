import { HttpStatus } from '@nestjs/common';
import { Tiso6391, Tiso31662 } from '@superawesome/freekws-regional-config';
import { SettingIdentifierDTO } from '@superawesome/freekws-settings-common';

export enum ESettingsServiceErrorCodes {
  CONSENT_REQUEST_MISSING_PARENT_EMAIL = 'consent_request_missing_parent_email',
  CONSENT_REQUEST_PARENT_NOT_PART_OF_CHILD_FAMILY = 'consent_request_parent_not_part_of_child_family',
  CONSENT_REQUEST_NO_SPACE_FOR_CHILD_IN_FAMILY_GROUP = 'consent_request_no_space_for_child_in_family_group',
  CONSENT_REQUEST_PARENT_IS_NOT_FAMILY_GROUP_MANAGER_OR_SUPERVISOR = 'consent_request_parent_is_not_family_group_manager_or_supervisor',
  CONSENT_REQUEST_MAIN_SETTING_MISSING = 'consent_request_main_setting_missing',
  CONSENT_REQUEST_LANGUAGE_MISMATCH = 'consent_request_language_mismatch',
}

type SendConsentEmailBaseParams = {
  userId: number;
  productId: string;
  dob?: string;
  parentEmail?: string;
  language?: Tiso6391;
  location?: Tiso31662;
};

type SendConsentEmailParamsWithPermissions = SendConsentEmailBaseParams & {
  permissions: string[];
  settings?: never;
};

type SendConsentEmailParamsWithSettings = SendConsentEmailBaseParams & {
  permissions?: never;
  settings: SettingIdentifierDTO[];
};

export type TSendConsentEmailParams = SendConsentEmailParamsWithPermissions | SendConsentEmailParamsWithSettings;

export type TGetUserSettings = {
  userId: number;
  productId: string;
  dateOfBirth?: string;
  location?: Tiso31662;
};

export type TRevokeUserPermissionsParams = Omit<TSendConsentEmailParams, 'permissions'>;

class SettingsServiceError extends Error {}

export class SettingsServiceParentMissingError extends SettingsServiceError {}

export class SettingsServiceConsentNotRequestedError extends SettingsServiceError {}

export class SettingsServicePermissionsNotFoundError extends SettingsServiceError {
  constructor(public readonly missingPermissions: string[]) {
    super('PERMISSIONS_NOT_FOUND');
  }
}

export class SettingsServiceParentNotInFamilyError extends SettingsServiceError {}

export type SettingsErrorResponse = {
  error: {
    statusCode: HttpStatus;
    code: string;
    message: string;
    errorCode: ESettingsServiceErrorCodes;
  };
  meta: {
    requestId: string;
    timestamp: string;
  };
};
