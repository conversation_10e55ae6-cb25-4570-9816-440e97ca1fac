import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { JwkController } from './jwk.controller';
import { JWK } from './jwk.entity';
import { JwkRepository } from './jwk.repository';
import { JWKService } from './jwk.service';

@Module({
  imports: [TypeOrmModule.forFeature([JWK])],
  controllers: [JwkController],
  providers: [JWKService, JwkRepository],
  exports: [JWKService],
})
export class JwkModule {}
