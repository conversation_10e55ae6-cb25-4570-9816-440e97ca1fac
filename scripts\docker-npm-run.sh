#!/bin/bash -ex

if [[ " $* " != *" --no-clean "* ]]; then
  if [ "$1" == "migration:generate" ] || [[ "$1" == *"test"* ]]; then
    echo "Cleaning docker data including volumes..."
    ./scripts/docker-clean-data.sh
  fi
  docker compose kill
fi


if [ -z "${CIRCLE_SHA1}" ]; then
  export TAG=local
  docker compose build
else
  export TAG=${CIRCLE_SHA1}-builder
  docker compose pull -q project
  if [ "$1" == "test:e2e" ]; then
    tests=$(circleci tests glob "test/**/**.e2e-spec.ts" | circleci tests split --split-by=timings)
    echo "Testing following files $tests"
  fi
fi

./scripts/docker-compose-volume-perms.sh

docker compose run -p 3000:80 -p 8080:8080 -p 9229:9229 ${COMPOSE_EXTRA_ARGS} project npm run "$@" $tests
