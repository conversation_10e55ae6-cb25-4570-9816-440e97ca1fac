# Sequential Load Testing with K6 Scenarios

A clean implementation using K6's built-in `scenarios` and `startTime` features for sequential endpoint testing.

## How It Works

**K6 automatically manages everything:**

1. **Sequential execution** - Each scenario has a `startTime` that starts after the previous one finishes
2. **Built-in ramp up/down** - Uses `ramping-vus` executor with stages
3. **Automatic cooldowns** - Scenarios naturally have gaps between them
4. **No manual timing logic** - K6 handles all the scheduling

## Files

- **`requests.ts`** - Reusable endpoint request functions
- **`sequential-scenarios-test.ts`** - K6 scenarios-based sequential test

## Configuration

| Environment Variable | Description | Default |
|---------------------|-------------|---------|
| `TARGET_QPS` | Target queries per second | `100` |
| `TEST_DURATION` | Seconds to test each endpoint | `60` |
| `COOLDOWN_DURATION` | Seconds between endpoints | `60` |
| `CLIENT_ID` | OAuth client ID | Required |
| `CLIENT_SECRET` | OAuth client secret | Required |
| `BASE_URL` | API base URL | `http://localhost:7001` |

## Usage

### Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your credentials
CLIENT_ID=your-actual-client-id
CLIENT_SECRET=your-actual-client-secret
BASE_URL=http://localhost:7001
```

### Run Tests

```bash
# Standard test (100 QPS)
npm run test:sequential-scenarios

# Stress test (300 QPS)
npm run test:sequential-scenarios-stress

# Custom configuration
npm run build:sequential-scenarios && dotenv -- k6 run dist/sequential-scenarios-test.js \
  --env TARGET_QPS=200 \
  --env TEST_DURATION=90 \
  --env COOLDOWN_DURATION=30
```

## How K6 Scenarios Work

Instead of manual timing, K6 automatically manages:

```javascript
scenarios: {
  healthcheck: {
    executor: 'ramping-vus',
    startTime: '0s',        // Starts immediately
    stages: [/* ramp up/down */]
  },
  getJwks: {
    executor: 'ramping-vus',
    startTime: '120s',      // Starts after healthcheck + cooldown
    stages: [/* ramp up/down */]
  },
  // ... more scenarios
}
```

## Endpoint Sequence

The test runs these 15 endpoints sequentially:

1. **healthcheck** (0s) - no auth
2. **getJwks** (120s) - no auth
3. **childAge** (240s) - no auth
4. **checkUsername** (360s) - no auth
5. **createUser** (480s) - auth required
6. **createUserV2** (600s) - auth required
7. **getUser** (720s) - auth required
8. **activateUser** (840s) - auth required - `POST /v1/users/:userId/apps`
9. **activateUserV2** (960s) - auth required - `POST /v2/apps/:appId/users/:userId/activate`
10. **requestPermissions** (1080s) - auth required
11. **getPermissions** (1200s) - auth required
12. **getPermissionsExtended** (1320s) - auth required
13. **reviewPermissions** (1440s) - auth required
14. **updateParentEmail** (1560s) - auth required
15. **deleteUser** (1680s) - auth required

*Times shown for 60s test + 60s cooldown*

## Load Pattern Per Endpoint

Each endpoint follows this VU pattern:

```
VUs
 ↑
 │     ┌─────────────┐
 │    ╱               ╲
 │   ╱                 ╲
 │  ╱                   ╲
 │ ╱                     ╲
 └╱───────────────────────╲─→ Time
  20%      60%       20%    Cooldown
Ramp Up  Steady    Ramp Down  (Next scenario)
```

## Advantages Over Manual Timing

✅ **K6 native** - Uses built-in features as intended
✅ **No manual calculations** - K6 handles all timing
✅ **Cleaner code** - No complex state management
✅ **More reliable** - K6's scenario engine is battle-tested
✅ **Better observability** - Each endpoint gets its own scenario metrics
✅ **Easier debugging** - Clear separation between endpoints

## Example Output

```
Sequential scenarios test starting:
- Target QPS: 100
- Target VUs: 10
- Test duration per endpoint: 60s
- Cooldown between endpoints: 60s
- Total endpoints: 14
- Estimated total time: 28.0 minutes

running (00m01s), 00/10 VUs, 0 complete and 0 interrupted iterations
healthcheck ✓ [======================================] 10 VUs  60s

running (02m01s), 00/10 VUs, 0 complete and 0 interrupted iterations
getJwks     ✓ [======================================] 10 VUs  60s
...
```

## Key Benefits

- **Automatic endpoint management** - K6 handles which endpoint runs when
- **Built-in load patterns** - Native ramp up/down with `ramping-vus`
- **Natural cooldowns** - Time gaps between scenarios provide cooldown
- **No complex logic** - Simple, clean implementation
- **Better metrics** - Each endpoint tagged separately by K6

This approach leverages K6's strengths instead of working around them!
